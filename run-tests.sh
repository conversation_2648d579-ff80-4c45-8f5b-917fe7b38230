#!/bin/bash

# 文件传输SDK测试运行脚本
# 用于执行完整的测试套件，验证所有功能的正确性

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Java环境
check_java() {
    log_info "检查Java环境..."

    # 由于指定的Java 8安装有问题，直接使用系统默认Java
    log_info "使用系统默认Java（项目配置为Java 8兼容）"

    # 确保使用系统默认Java
    unset JAVA_HOME
    export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8"

    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未在PATH中"
        exit 1
    fi

    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "Java版本: $JAVA_VERSION"

    # 验证Java版本
    if [[ "$JAVA_VERSION" =~ ^1\.8\. ]]; then
        log_info "使用Java 8，完全兼容"
    elif [[ "$JAVA_VERSION" =~ ^11\. ]] || [[ "$JAVA_VERSION" =~ ^17\. ]] || [[ "$JAVA_VERSION" =~ ^21\. ]]; then
        log_warning "使用Java $JAVA_VERSION，项目配置为Java 8，但应该向后兼容"
    else
        log_warning "当前Java版本: $JAVA_VERSION，可能会有兼容性问题"
    fi

    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或未在PATH中"
        exit 1
    fi

    MVN_VERSION=$(mvn -version | head -n 1)
    log_info "Maven版本: $MVN_VERSION"
}

# 清理环境
cleanup() {
    log_info "清理测试环境..."
    
    # 清理测试数据目录
    if [ -d "./test-data" ]; then
        rm -rf ./test-data
        log_info "清理测试数据目录"
    fi
    
    # 清理临时文件
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "test-*.dat" -delete 2>/dev/null || true
    
    log_success "环境清理完成"
}

# 编译项目
compile_project() {
    log_info "编译项目..."

    # 设置Maven使用指定的Java版本
    export MAVEN_OPTS="-Djava.home=$JAVA_HOME"

    if ! mvn clean compile -q -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8; then
        log_error "项目编译失败"
        exit 1
    fi

    log_success "项目编译成功"
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    # 运行服务端SDK单元测试
    log_info "运行服务端SDK单元测试..."
    if ! mvn test -pl file-transfer-server-sdk -q -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8; then
        log_error "服务端SDK单元测试失败"
        return 1
    fi
    log_success "服务端SDK单元测试通过"

    # 运行客户端SDK单元测试
    log_info "运行客户端SDK单元测试..."
    if ! mvn test -pl file-transfer-client-sdk -q -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8; then
        log_error "客户端SDK单元测试失败"
        return 1
    fi
    log_success "客户端SDK单元测试通过"
    
    log_success "所有单元测试通过"
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    # 启动测试服务器
    log_info "启动测试服务器..."
    mvn spring-boot:run -pl file-transfer-demo -Dspring-boot.run.arguments="--server.port=49012" &
    SERVER_PID=$!
    
    # 等待服务器启动
    log_info "等待服务器启动..."
    sleep 10
    
    # 检查服务器是否启动成功
    if ! curl -s http://localhost:49012/api/file/health > /dev/null; then
        log_error "测试服务器启动失败"
        kill $SERVER_PID 2>/dev/null || true
        return 1
    fi
    log_success "测试服务器启动成功"
    
    # 运行集成测试
    log_info "运行端到端测试..."
    if ! mvn test -pl file-transfer-demo -Dtest=EndToEndTransferTest -Dserver.port=49012; then
        log_error "端到端测试失败"
        kill $SERVER_PID 2>/dev/null || true
        return 1
    fi
    log_success "端到端测试通过"
    
    # 关闭测试服务器
    log_info "关闭测试服务器..."
    kill $SERVER_PID 2>/dev/null || true
    sleep 2
    
    log_success "集成测试完成"
}

# 运行性能测试
run_performance_tests() {
    log_info "运行性能测试..."
    
    if ! mvn test -pl file-transfer-server-sdk -Dtest=FileTransferPerformanceTest; then
        log_warning "性能测试失败或未达到预期性能指标"
        return 1
    fi
    
    log_success "性能测试通过"
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    # 生成测试覆盖率报告
    if command -v mvn &> /dev/null; then
        mvn jacoco:report -q 2>/dev/null || log_warning "无法生成覆盖率报告"
    fi
    
    # 收集测试结果
    TOTAL_TESTS=0
    FAILED_TESTS=0
    
    for module in file-transfer-server-sdk file-transfer-client-sdk file-transfer-demo; do
        if [ -d "./$module/target/surefire-reports" ]; then
            MODULE_TESTS=$(find ./$module/target/surefire-reports -name "*.xml" | wc -l)
            MODULE_FAILURES=$(grep -l "failure\|error" ./$module/target/surefire-reports/*.xml 2>/dev/null | wc -l)
            
            TOTAL_TESTS=$((TOTAL_TESTS + MODULE_TESTS))
            FAILED_TESTS=$((FAILED_TESTS + MODULE_FAILURES))
            
            log_info "$module: $MODULE_TESTS 个测试，$MODULE_FAILURES 个失败"
        fi
    done
    
    PASSED_TESTS=$((TOTAL_TESTS - FAILED_TESTS))
    
    echo ""
    echo "=========================================="
    echo "           测试结果汇总"
    echo "=========================================="
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "测试结果: ${GREEN}全部通过${NC}"
    else
        echo -e "测试结果: ${RED}有失败${NC}"
    fi
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "      文件传输SDK测试套件"
    echo "=========================================="
    
    # 解析命令行参数
    RUN_UNIT=true
    RUN_INTEGRATION=true
    RUN_PERFORMANCE=false
    CLEANUP_BEFORE=true
    CLEANUP_AFTER=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --unit-only)
                RUN_INTEGRATION=false
                RUN_PERFORMANCE=false
                shift
                ;;
            --integration-only)
                RUN_UNIT=false
                RUN_PERFORMANCE=false
                shift
                ;;
            --performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --no-cleanup)
                CLEANUP_BEFORE=false
                CLEANUP_AFTER=false
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --unit-only      仅运行单元测试"
                echo "  --integration-only 仅运行集成测试"
                echo "  --performance    包含性能测试"
                echo "  --no-cleanup     不清理测试环境"
                echo "  --help          显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 检查环境
    check_java
    
    # 清理环境
    if [ "$CLEANUP_BEFORE" = true ]; then
        cleanup
    fi
    
    # 编译项目
    compile_project
    
    # 运行测试
    TEST_FAILED=false
    
    if [ "$RUN_UNIT" = true ]; then
        if ! run_unit_tests; then
            TEST_FAILED=true
        fi
    fi
    
    if [ "$RUN_INTEGRATION" = true ]; then
        if ! run_integration_tests; then
            TEST_FAILED=true
        fi
    fi
    
    if [ "$RUN_PERFORMANCE" = true ]; then
        if ! run_performance_tests; then
            log_warning "性能测试未通过，但不影响整体测试结果"
        fi
    fi
    
    # 生成测试报告
    generate_test_report
    
    # 清理环境
    if [ "$CLEANUP_AFTER" = true ]; then
        cleanup
    fi
    
    # 返回结果
    if [ "$TEST_FAILED" = true ]; then
        log_error "测试套件执行失败"
        exit 1
    else
        log_success "测试套件执行成功"
        exit 0
    fi
}

# 捕获中断信号，确保清理
trap cleanup EXIT

# 执行主函数
main "$@"

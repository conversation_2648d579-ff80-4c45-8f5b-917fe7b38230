package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;

/**
 * 认证服务测试
 */
@DisplayName("认证服务测试")
class AuthServiceTest {
    
    private AuthService authService;
    private FileTransferProperties properties;
    
    private final String testUser = "test-user";
    private final String testSecretKey = "test-secret-key-2024";
    
    @BeforeEach
    void setUp() throws Exception {
        // 创建配置
        properties = new FileTransferProperties();
        properties.setEnabled(true);
        
        // 配置测试用户
        UserConfig userConfig = new UserConfig();
        userConfig.setSecretKey(testSecretKey);
        userConfig.setStoragePath("./test-storage");
        userConfig.setUploadRateLimit(10485760L); // 10MB/s
        userConfig.setDownloadRateLimit(10485760L);
        userConfig.setDefaultChunkSize(1048576L); // 1MB
        userConfig.setMaxFileSize(104857600L); // 100MB
        userConfig.setMaxInMemorySize(10485760L); // 10MB
        userConfig.setFastUploadEnabled(true);
        userConfig.setRateLimitEnabled(false);
        
        Map<String, UserConfig> users = new HashMap<>();
        users.put(testUser, userConfig);
        properties.setUsers(users);
        
        // 创建认证服务
        authService = new AuthService();
        
        // 使用反射设置properties字段
        java.lang.reflect.Field propertiesField = AuthService.class.getDeclaredField("properties");
        propertiesField.setAccessible(true);
        propertiesField.set(authService, properties);
    }
    
    @Test
    @DisplayName("用户认证令牌生成测试")
    void testGenerateAuthToken() {
        // 生成认证令牌
        String authToken = authService.generateAuthToken(testUser, testSecretKey);
        
        // 验证令牌不为空
        assertNotNull(authToken);
        assertFalse(authToken.trim().isEmpty());
        
        // 验证令牌格式（应该包含时间戳和签名）
        assertTrue(authToken.contains(":"));
        String[] parts = authToken.split(":");
        assertEquals(3, parts.length); // user:timestamp:signature
        assertEquals(testUser, parts[0]);
        
        // 验证时间戳是数字
        assertDoesNotThrow(() -> {
            Long.parseLong(parts[1]);
        });
        
        // 验证签名不为空
        assertNotNull(parts[2]);
        assertFalse(parts[2].trim().isEmpty());
    }
    
    @Test
    @DisplayName("用户认证验证测试")
    void testAuthenticate() {
        // 生成有效的认证令牌
        String validToken = authService.generateAuthToken(testUser, testSecretKey);
        
        // 验证有效令牌
        assertTrue(authService.authenticate(testUser, validToken));
        
        // 验证无效用户名
        assertFalse(authService.authenticate("invalid-user", validToken));
        
        // 验证无效令牌
        assertFalse(authService.authenticate(testUser, "invalid-token"));
        
        // 验证空令牌
        assertFalse(authService.authenticate(testUser, ""));
        assertFalse(authService.authenticate(testUser, null));
        
        // 验证空用户名
        assertFalse(authService.authenticate("", validToken));
        assertFalse(authService.authenticate(null, validToken));
    }
    
    @Test
    @DisplayName("用户配置获取测试")
    void testGetUserConfig() {
        // 获取存在的用户配置
        UserConfig config = authService.getUserConfig(testUser);
        assertNotNull(config);
        assertEquals(testSecretKey, config.getSecretKey());
        assertEquals(1048576L, config.getDefaultChunkSize());
        assertEquals(104857600L, config.getMaxFileSize());
        assertTrue(config.getFastUploadEnabled());
        assertFalse(config.getRateLimitEnabled());
        
        // 获取不存在的用户配置（应该返回默认配置）
        UserConfig defaultConfig = authService.getUserConfig("nonexistent-user");
        assertNotNull(defaultConfig);
        // 默认配置不应该有密钥
        assertNull(defaultConfig.getSecretKey());
        // 应该有默认值
        assertTrue(defaultConfig.getDefaultChunkSize() > 0);
        assertTrue(defaultConfig.getMaxFileSize() > 0);
    }
    
    @Test
    @DisplayName("HMAC签名计算测试")
    void testHmacSignature() throws Exception {
        String data = "test-data-for-hmac";
        String key = "test-secret-key";
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = AuthService.class.getDeclaredMethod("calculateHmacSha256", String.class, String.class);
        method.setAccessible(true);
        
        String signature1 = (String) method.invoke(authService, data, key);
        String signature2 = (String) method.invoke(authService, data, key);
        
        // 验证签名一致性
        assertNotNull(signature1);
        assertNotNull(signature2);
        assertEquals(signature1, signature2);
        
        // 验证不同数据产生不同签名
        String signature3 = (String) method.invoke(authService, "different-data", key);
        assertNotEquals(signature1, signature3);
        
        // 验证不同密钥产生不同签名
        String signature4 = (String) method.invoke(authService, data, "different-key");
        assertNotEquals(signature1, signature4);
    }
    
    @Test
    @DisplayName("令牌过期测试")
    void testTokenExpiration() throws Exception {
        // 创建一个过期的令牌（使用过去的时间戳）
        long expiredTimestamp = System.currentTimeMillis() - (2 * 60 * 60 * 1000); // 2小时前
        
        // 使用反射创建过期令牌
        java.lang.reflect.Method method = AuthService.class.getDeclaredMethod("calculateHmacSha256", String.class, String.class);
        method.setAccessible(true);
        
        String data = testUser + ":" + expiredTimestamp;
        String signature = (String) method.invoke(authService, data, testSecretKey);
        String expiredToken = testUser + ":" + expiredTimestamp + ":" + signature;
        
        // 验证过期令牌被拒绝
        assertFalse(authService.authenticate(testUser, expiredToken));
    }
    
    @Test
    @DisplayName("令牌格式验证测试")
    void testTokenFormatValidation() {
        // 测试格式错误的令牌
        String[] invalidTokens = {
            "invalid-format",
            "user:timestamp", // 缺少签名
            "user:invalid-timestamp:signature", // 时间戳格式错误
            ":timestamp:signature", // 缺少用户名
            "user::signature", // 缺少时间戳
            "user:timestamp:", // 缺少签名
            "user:timestamp:signature:extra" // 多余的部分
        };
        
        for (String invalidToken : invalidTokens) {
            assertFalse(authService.authenticate(testUser, invalidToken), 
                    "无效令牌应该被拒绝: " + invalidToken);
        }
    }
    
    @Test
    @DisplayName("用户配置默认值测试")
    void testUserConfigDefaults() {
        // 创建没有用户配置的properties
        FileTransferProperties emptyProperties = new FileTransferProperties();
        emptyProperties.setEnabled(true);
        emptyProperties.setUsers(new HashMap<>());
        
        AuthService emptyAuthService = new AuthService();
        try {
            java.lang.reflect.Field propertiesField = AuthService.class.getDeclaredField("properties");
            propertiesField.setAccessible(true);
            propertiesField.set(emptyAuthService, emptyProperties);
        } catch (Exception e) {
            fail("设置properties失败: " + e.getMessage());
        }
        
        // 获取默认配置
        UserConfig defaultConfig = emptyAuthService.getUserConfig("any-user");
        assertNotNull(defaultConfig);
        
        // 验证默认值
        assertNull(defaultConfig.getSecretKey());
        assertEquals(2097152L, defaultConfig.getDefaultChunkSize()); // 2MB
        assertEquals(104857600L, defaultConfig.getMaxFileSize()); // 100MB
        assertEquals(10485760L, defaultConfig.getMaxInMemorySize()); // 10MB
        assertEquals(10485760L, defaultConfig.getUploadRateLimit()); // 10MB/s
        assertEquals(10485760L, defaultConfig.getDownloadRateLimit()); // 10MB/s
        assertTrue(defaultConfig.getFastUploadEnabled());
        assertTrue(defaultConfig.getRateLimitEnabled());
    }
    
    @Test
    @DisplayName("并发认证测试")
    void testConcurrentAuthentication() throws InterruptedException {
        final int threadCount = 10;
        final int operationsPerThread = 100;
        
        Thread[] threads = new Thread[threadCount];
        final boolean[] results = new boolean[threadCount];
        
        // 创建多个线程同时进行认证
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                boolean allSuccess = true;
                for (int j = 0; j < operationsPerThread; j++) {
                    String token = authService.generateAuthToken(testUser, testSecretKey);
                    if (!authService.authenticate(testUser, token)) {
                        allSuccess = false;
                        break;
                    }
                }
                results[threadIndex] = allSuccess;
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证所有线程都成功
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], "线程 " + i + " 认证失败");
        }
    }
}

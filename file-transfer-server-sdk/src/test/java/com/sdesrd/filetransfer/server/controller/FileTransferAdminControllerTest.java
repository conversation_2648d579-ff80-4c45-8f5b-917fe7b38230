package com.sdesrd.filetransfer.server.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import javax.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.sdesrd.filetransfer.server.dto.ApiResult;
import com.sdesrd.filetransfer.server.dto.SystemHealthResponse;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService.TransferStatistics;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

/**
 * 文件传输管理控制器测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("文件传输管理控制器测试")
class FileTransferAdminControllerTest {
    
    @Mock
    private FileTransferMonitorService monitorService;
    
    @Mock
    private HttpServletRequest httpRequest;
    
    @InjectMocks
    private FileTransferAdminController adminController;
    
    private TransferStatistics mockStatistics;
    
    @BeforeEach
    void setUp() {
        // 创建模拟的传输统计数据
        mockStatistics = new TransferStatistics();
        mockStatistics.setPendingCount(5);
        mockStatistics.setTransferringCount(10);
        mockStatistics.setCompletedCount(100);
        mockStatistics.setFailedCount(2);
        mockStatistics.setSuccessRate(98.04);
    }
    
    @Test
    @DisplayName("获取传输统计信息 - 成功")
    void testGetStatistics_Success() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class)) {
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            when(monitorService.getTransferStatistics()).thenReturn(mockStatistics);
            
            // 执行测试
            ApiResult<TransferStatistics> result = adminController.getStatistics(httpRequest);
            
            // 验证结果
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            assertEquals(5, result.getData().getPendingCount());
            assertEquals(10, result.getData().getTransferringCount());
            assertEquals(100, result.getData().getCompletedCount());
            assertEquals(2, result.getData().getFailedCount());
            assertEquals(98.04, result.getData().getSuccessRate(), 0.01);
            
            // 验证方法调用
            verify(monitorService).getTransferStatistics();
        }
    }
    
    @Test
    @DisplayName("获取传输统计信息 - 异常处理")
    void testGetStatistics_Exception() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class)) {
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            when(monitorService.getTransferStatistics()).thenThrow(new RuntimeException("数据库连接失败"));
            
            // 执行测试
            ApiResult<TransferStatistics> result = adminController.getStatistics(httpRequest);
            
            // 验证结果
            assertFalse(result.isSuccess());
            assertTrue(result.getMessage().contains("获取传输统计信息失败"));
            assertNull(result.getData());
        }
    }
    
    @Test
    @DisplayName("系统健康检查 - 成功")
    void testSystemHealth_Success() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class)) {
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            when(monitorService.getTransferStatistics()).thenReturn(mockStatistics);
            
            // 执行测试
            ApiResult<SystemHealthResponse> result = adminController.systemHealth(httpRequest);
            
            // 验证结果
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            
            SystemHealthResponse healthResponse = result.getData();
            assertEquals("UP", healthResponse.getStatus());
            assertNotNull(healthResponse.getTimestamp());
            assertNotNull(healthResponse.getTotalMemory());
            assertNotNull(healthResponse.getFreeMemory());
            assertNotNull(healthResponse.getUsedMemory());
            assertNotNull(healthResponse.getMaxMemory());
            assertNotNull(healthResponse.getTransferStats());
            
            // 验证传输统计数据
            assertEquals(mockStatistics.getCompletedCount(), healthResponse.getTransferStats().getCompletedCount());
            assertEquals(mockStatistics.getFailedCount(), healthResponse.getTransferStats().getFailedCount());
            
            // 验证方法调用
            verify(monitorService).getTransferStatistics();
        }
    }
    
    @Test
    @DisplayName("清理限流器缓存 - 成功")
    void testClearRateLimiters_Success() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class);
             MockedStatic<RateLimitUtils> mockedRateLimitUtils = mockStatic(RateLimitUtils.class)) {
            
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            // 执行测试
            ApiResult<String> result = adminController.clearRateLimiters(httpRequest);
            
            // 验证结果
            assertTrue(result.isSuccess());
            assertEquals("限流器缓存清理完成", result.getData());
            
            // 验证方法调用
            mockedRateLimitUtils.verify(RateLimitUtils::clearAllRateLimiters);
        }
    }
    
    @Test
    @DisplayName("清理限流器缓存 - 异常处理")
    void testClearRateLimiters_Exception() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class);
             MockedStatic<RateLimitUtils> mockedRateLimitUtils = mockStatic(RateLimitUtils.class)) {
            
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            mockedRateLimitUtils.when(RateLimitUtils::clearAllRateLimiters)
                    .thenThrow(new RuntimeException("清理失败"));
            
            // 执行测试
            ApiResult<String> result = adminController.clearRateLimiters(httpRequest);
            
            // 验证结果
            assertFalse(result.isSuccess());
            assertTrue(result.getMessage().contains("清理限流器缓存失败"));
        }
    }
    
    @Test
    @DisplayName("内存使用率计算测试")
    void testMemoryUsageCalculation() {
        // 创建健康检查响应对象
        SystemHealthResponse healthResponse = new SystemHealthResponse();
        healthResponse.setUsedMemory(512 * 1024 * 1024L); // 512MB
        healthResponse.setMaxMemory(1024 * 1024 * 1024L); // 1GB
        
        // 验证内存使用率计算
        assertEquals(50.0, healthResponse.getMemoryUsagePercentage(), 0.01);
        
        // 验证格式化内存信息
        String formattedInfo = healthResponse.getFormattedMemoryInfo();
        assertTrue(formattedInfo.contains("512MB"));
        assertTrue(formattedInfo.contains("1024MB"));
        assertTrue(formattedInfo.contains("50.00%"));
    }
}

package com.sdesrd.filetransfer.server.service;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 认证服务
 */
@Slf4j
@Service
public class AuthService {
    
    @Autowired
    private FileTransferProperties properties;
    
    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String AUTH_HEADER = "X-File-Transfer-Auth";
    private static final String USER_HEADER = "X-File-Transfer-User";
    private static final long TOKEN_EXPIRE_SECONDS = 300; // 5分钟
    
    /**
     * 验证客户端认证信息
     *
     * @param username 用户名
     * @param authToken 认证令牌
     * @return 是否验证通过
     */
    public boolean authenticate(String username, String authToken) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(authToken)) {
            log.warn("用户名或认证令牌为空");
            return false;
        }

        UserConfig userConfig = properties.getUsers().get(username);
        if (userConfig == null) {
            log.warn("用户不存在: {}", username);
            return false;
        }

        if (!StringUtils.hasText(userConfig.getSecretKey())) {
            log.warn("用户密钥未配置: {}", username);
            return false;
        }

        try {
            // 解析认证令牌：user:timestamp:signature
            String[] parts = authToken.split(":");
            if (parts.length != 3) {
                log.warn("认证令牌格式错误: {}", authToken);
                return false;
            }

            String tokenUser = parts[0];
            long timestamp = Long.parseLong(parts[1]);
            String signature = parts[2];

            // 验证用户名匹配
            if (!username.equals(tokenUser)) {
                log.warn("令牌用户名不匹配: {} vs {}", username, tokenUser);
                return false;
            }

            // 检查令牌是否过期
            if (isTokenExpired(timestamp)) {
                log.warn("认证令牌已过期: {}", username);
                return false;
            }

            // 验证签名
            String expectedSignature = calculateHmacSha256(username + ":" + timestamp, userConfig.getSecretKey());
            if (!expectedSignature.equals(signature)) {
                log.warn("认证签名验证失败: {}", username);
                return false;
            }

            log.debug("用户认证成功: {}", username);
            return true;

        } catch (Exception e) {
            log.error("认证过程中发生异常: {}", username, e);
            return false;
        }
    }
    
    /**
     * 生成客户端认证令牌
     *
     * @param username 用户名
     * @param secretKey 密钥
     * @return 认证令牌
     */
    public String generateAuthToken(String username, String secretKey) {
        try {
            long timestamp = System.currentTimeMillis();
            String signature = calculateHmacSha256(username + ":" + timestamp, secretKey);
            return username + ":" + timestamp + ":" + signature;
        } catch (Exception e) {
            log.error("生成认证令牌失败: {}", username, e);
            throw new RuntimeException("生成认证令牌失败", e);
        }
    }
    
    /**
     * 获取用户配置
     * 
     * @param username 用户名
     * @return 用户配置
     */
    public UserConfig getUserConfig(String username) {
        return properties.getUserConfig(username);
    }
    
    /**
     * 计算HMAC-SHA256签名（供测试使用的公共方法）
     */
    private String calculateHmacSha256(String data, String secretKey)
            throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance(HMAC_SHA256);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
        mac.init(secretKeySpec);
        byte[] signature = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signature);
    }
    

    
    /**
     * 检查令牌是否过期
     */
    private boolean isTokenExpired(long timestamp) {
        long currentTime = System.currentTimeMillis();
        return (currentTime - timestamp) > (TOKEN_EXPIRE_SECONDS * 1000); // 转换为毫秒
    }
    

} 
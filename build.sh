#!/bin/bash

# 文件传输SDK自动编译脚本
# 功能：自动编译整个项目，处理所有模块和依赖关系
# 作者：Augment Agent
# 版本：1.0.0

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="文件传输SDK自动编译脚本"

# Java 8 JDK路径配置
readonly JAVA8_HOME="$HOME/.jdks/corretto-1.8.0_452"

# 项目模块列表（父POM中包含的模块）
readonly PROJECT_MODULES=(
    "file-transfer-server-sdk"
    "file-transfer-client-sdk"
    "file-transfer-demo"
)

# 独立模块列表（不在父POM中的模块）
readonly STANDALONE_MODULES=(
    "file-transfer-server-standalone"
)

# 编译超时时间（秒）
readonly COMPILE_TIMEOUT=600

# 日志文件路径
readonly LOG_DIR="./logs"
readonly BUILD_LOG="$LOG_DIR/build-$(date +%Y%m%d_%H%M%S).log"

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_PURPLE='\033[0;35m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
    echo "[INFO] ${timestamp} - ${message}" >> "$BUILD_LOG"
}

# 成功日志
log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
    echo "[SUCCESS] ${timestamp} - ${message}" >> "$BUILD_LOG"
}

# 警告日志
log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
    echo "[WARNING] ${timestamp} - ${message}" >> "$BUILD_LOG"
}

# 错误日志
log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
    echo "[ERROR] ${timestamp} - ${message}" >> "$BUILD_LOG"
}

# 步骤日志
log_step() {
    local step_number="$1"
    local step_name="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_PURPLE}[STEP ${step_number}]${COLOR_NC} ${timestamp} - ${step_name}"
    echo "[STEP ${step_number}] ${timestamp} - ${step_name}" >> "$BUILD_LOG"
}

# ==================== 工具函数 ====================

# 初始化日志目录
init_logging() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
    fi
    
    # 创建构建日志文件
    touch "$BUILD_LOG"
    log_info "构建日志文件：$BUILD_LOG"
}

# 显示脚本头部信息
show_header() {
    echo "=========================================="
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "=========================================="
}

# 检查命令是否存在
check_command() {
    local command="$1"
    local description="$2"
    
    if ! command -v "$command" &> /dev/null; then
        log_error "$description 未安装或未在PATH中：$command"
        return 1
    fi
    return 0
}

# 检查Java 8环境
check_java8_environment() {
    log_step "1" "检查Java 8环境"
    
    # 检查指定的Java 8 JDK是否存在
    if [ -d "$JAVA8_HOME" ] && [ -x "$JAVA8_HOME/bin/java" ]; then
        export JAVA_HOME="$JAVA8_HOME"
        export PATH="$JAVA8_HOME/bin:$PATH"
        log_info "使用指定的Java 8 JDK：$JAVA8_HOME"
    else
        log_warning "指定的Java 8 JDK不存在：$JAVA8_HOME"
        log_info "使用系统默认Java（确保兼容Java 8）"
        
        # 清除JAVA_HOME，使用系统默认
        unset JAVA_HOME
    fi
    
    # 验证Java命令可用性
    if ! check_command "java" "Java运行时"; then
        return 1
    fi
    
    # 获取Java版本信息
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "当前Java版本：$java_version"
    
    # 验证Java版本兼容性
    if [[ "$java_version" =~ ^1\.8\. ]]; then
        log_success "使用Java 8，完全兼容"
    elif [[ "$java_version" =~ ^(11|17|21)\. ]]; then
        log_warning "使用Java $java_version，项目配置为Java 8，但应该向后兼容"
    else
        log_warning "当前Java版本：$java_version，可能存在兼容性问题"
    fi
    
    return 0
}

# 检查Maven环境
check_maven_environment() {
    log_step "2" "检查Maven环境"
    
    # 检查Maven命令
    if ! check_command "mvn" "Apache Maven"; then
        return 1
    fi
    
    # 获取Maven版本信息
    local maven_version=$(mvn -version | head -n 1)
    log_info "Maven版本：$maven_version"
    
    # 配置Maven使用Java 8
    export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxPermSize=512m"
    
    if [ -n "$JAVA_HOME" ]; then
        export MAVEN_OPTS="$MAVEN_OPTS -Djava.home=$JAVA_HOME"
        log_info "Maven配置使用Java：$JAVA_HOME"
    fi
    
    log_info "Maven选项：$MAVEN_OPTS"
    log_success "Maven环境检查完成"
    
    return 0
}

# 验证项目结构
validate_project_structure() {
    log_step "3" "验证项目结构"
    
    # 检查根目录pom.xml
    if [ ! -f "pom.xml" ]; then
        log_error "根目录pom.xml文件不存在"
        return 1
    fi
    log_info "根目录pom.xml文件存在"
    
    # 检查各个模块目录
    local missing_modules=()
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            missing_modules+=("$module")
        else
            log_info "模块目录存在：$module"
            
            # 检查模块的pom.xml
            if [ ! -f "$module/pom.xml" ]; then
                log_warning "模块pom.xml不存在：$module/pom.xml"
            fi
        fi
    done
    
    # 报告缺失的模块
    if [ ${#missing_modules[@]} -gt 0 ]; then
        log_warning "以下模块目录不存在：${missing_modules[*]}"
        log_warning "将跳过这些模块的编译"
    fi
    
    log_success "项目结构验证完成"
    return 0
}

# 清理构建环境
clean_build_environment() {
    log_step "4" "清理构建环境"
    
    log_info "清理Maven构建缓存..."
    
    # 清理根目录target
    if [ -d "target" ]; then
        rm -rf target
        log_info "清理根目录target目录"
    fi
    
    # 清理各模块的target目录
    for module in "${PROJECT_MODULES[@]}"; do
        if [ -d "$module" ] && [ -d "$module/target" ]; then
            rm -rf "$module/target"
            log_info "清理模块target目录：$module"
        fi
    done
    
    # 清理临时文件
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "*.log" -path "*/target/*" -delete 2>/dev/null || true
    
    log_success "构建环境清理完成"
    return 0
}

# 编译项目
compile_project() {
    log_step "5" "编译项目"

    local start_time=$(date +%s)

    log_info "开始编译整个项目..."
    log_info "编译命令：mvn clean compile -T 1C"

    # 执行Maven编译，使用并行编译提高速度
    if timeout "$COMPILE_TIMEOUT" mvn clean compile -T 1C \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        -Dmaven.compiler.encoding=UTF-8 \
        -Dproject.build.sourceEncoding=UTF-8 \
        >> "$BUILD_LOG" 2>&1; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_success "项目编译成功，耗时：${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_error "项目编译失败，耗时：${duration}秒"
        log_error "详细错误信息请查看日志文件：$BUILD_LOG"
        return 1
    fi
}

# 安装项目到本地仓库
install_project() {
    log_step "6" "安装项目到本地Maven仓库"

    local start_time=$(date +%s)

    log_info "开始安装项目到本地Maven仓库..."
    log_info "安装命令：mvn install -DskipTests -T 1C"

    # 执行Maven安装，跳过测试以提高速度
    if timeout "$COMPILE_TIMEOUT" mvn install -DskipTests -T 1C \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        -Dmaven.compiler.encoding=UTF-8 \
        -Dproject.build.sourceEncoding=UTF-8 \
        >> "$BUILD_LOG" 2>&1; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_success "项目安装成功，耗时：${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_error "项目安装失败，耗时：${duration}秒"
        log_error "详细错误信息请查看日志文件：$BUILD_LOG"
        return 1
    fi
}

# 验证编译结果
verify_build_results() {
    log_step "7" "验证编译结果"

    local success_count=0
    local total_count=0

    # 检查各模块的编译结果
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            continue
        fi

        total_count=$((total_count + 1))

        # 检查target/classes目录是否存在
        if [ -d "$module/target/classes" ]; then
            local class_count=$(find "$module/target/classes" -name "*.class" | wc -l)
            if [ "$class_count" -gt 0 ]; then
                log_info "模块编译成功：$module (生成 $class_count 个class文件)"
                success_count=$((success_count + 1))
            else
                log_warning "模块编译异常：$module (未生成class文件)"
            fi
        else
            log_warning "模块编译失败：$module (target/classes目录不存在)"
        fi

        # 检查JAR文件是否生成
        if [ -d "$module/target" ]; then
            local jar_files=$(find "$module/target" -name "*.jar" | wc -l)
            if [ "$jar_files" -gt 0 ]; then
                log_info "模块JAR文件生成：$module ($jar_files 个JAR文件)"
            fi
        fi
    done

    # 输出验证结果
    log_info "编译验证结果：$success_count/$total_count 个模块编译成功"

    if [ "$success_count" -eq "$total_count" ]; then
        log_success "所有模块编译验证通过"
        return 0
    else
        log_warning "部分模块编译验证失败"
        return 1
    fi
}

# 生成构建报告
generate_build_report() {
    log_step "8" "生成构建报告"

    local report_file="$LOG_DIR/build-report-$(date +%Y%m%d_%H%M%S).txt"

    {
        echo "=========================================="
        echo "        文件传输SDK构建报告"
        echo "=========================================="
        echo "构建时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "脚本版本：$SCRIPT_VERSION"
        echo "Java版本：$(java -version 2>&1 | head -n 1)"
        echo "Maven版本：$(mvn -version | head -n 1)"
        echo ""

        echo "项目模块："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ -d "$module" ]; then
                echo "  ✓ $module"
            else
                echo "  ✗ $module (目录不存在)"
            fi
        done
        echo ""

        echo "编译结果："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ ! -d "$module" ]; then
                continue
            fi

            if [ -d "$module/target/classes" ]; then
                local class_count=$(find "$module/target/classes" -name "*.class" | wc -l)
                echo "  $module: $class_count 个class文件"
            else
                echo "  $module: 编译失败"
            fi
        done
        echo ""

        echo "JAR文件："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ ! -d "$module" ]; then
                continue
            fi

            if [ -d "$module/target" ]; then
                find "$module/target" -name "*.jar" | while read jar_file; do
                    local jar_size=$(du -h "$jar_file" | cut -f1)
                    echo "  $(basename "$jar_file"): $jar_size"
                done
            fi
        done
        echo ""

        echo "构建日志：$BUILD_LOG"
        echo "=========================================="

    } > "$report_file"

    log_info "构建报告已生成：$report_file"

    # 显示报告内容
    cat "$report_file"

    return 0
}

# 清理函数（脚本退出时调用）
cleanup_on_exit() {
    local exit_code=$?

    if [ $exit_code -ne 0 ]; then
        log_error "构建过程中发生错误，退出码：$exit_code"
        log_info "详细错误信息请查看日志文件：$BUILD_LOG"
    fi

    # 恢复原始环境变量
    if [ -n "$ORIGINAL_JAVA_HOME" ]; then
        export JAVA_HOME="$ORIGINAL_JAVA_HOME"
    fi

    if [ -n "$ORIGINAL_PATH" ]; then
        export PATH="$ORIGINAL_PATH"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --compile-only    仅编译，不安装到本地仓库"
    echo "  --skip-verify     跳过编译结果验证"
    echo "  --no-report       不生成构建报告"
    echo "  --verbose         显示详细输出"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 完整构建流程"
    echo "  $0 --compile-only # 仅编译项目"
    echo "  $0 --verbose      # 显示详细输出"
    echo ""
}

# 主函数
main() {
    # 保存原始环境变量
    export ORIGINAL_JAVA_HOME="$JAVA_HOME"
    export ORIGINAL_PATH="$PATH"

    # 设置退出时清理
    trap cleanup_on_exit EXIT

    # 解析命令行参数
    local compile_only=false
    local skip_verify=false
    local no_report=false
    local verbose=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --compile-only)
                compile_only=true
                shift
                ;;
            --skip-verify)
                skip_verify=true
                shift
                ;;
            --no-report)
                no_report=true
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 显示脚本头部信息
    show_header

    # 初始化日志
    init_logging

    # 如果启用详细模式，设置bash调试
    if [ "$verbose" = true ]; then
        set -x
        log_info "启用详细输出模式"
    fi

    # 执行构建步骤
    local build_failed=false

    # 步骤1-3：环境检查和项目验证
    if ! check_java8_environment; then
        build_failed=true
    elif ! check_maven_environment; then
        build_failed=true
    elif ! validate_project_structure; then
        build_failed=true
    elif ! clean_build_environment; then
        build_failed=true
    elif ! compile_project; then
        build_failed=true
    elif [ "$compile_only" = false ] && ! install_project; then
        build_failed=true
    elif [ "$skip_verify" = false ] && ! verify_build_results; then
        build_failed=true
    fi

    # 生成构建报告
    if [ "$no_report" = false ]; then
        generate_build_report
    fi

    # 返回结果
    if [ "$build_failed" = true ]; then
        log_error "构建失败"
        exit 1
    else
        log_success "构建成功完成"
        exit 0
    fi
}

# 执行主函数
main "$@"

#!/bin/bash

# 文件传输SDK自动测试脚本
# 功能：运行完整的测试套件，包括单元测试、集成测试、性能测试和端到端测试
# 作者：Augment Agent
# 版本：1.0.0

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="文件传输SDK自动测试脚本"

# Java 8 JDK路径配置
readonly JAVA8_HOME="$HOME/.jdks/corretto-1.8.0_452"

# 项目模块列表
readonly PROJECT_MODULES=(
    "file-transfer-server-sdk"
    "file-transfer-client-sdk" 
    "file-transfer-demo"
)

# 测试配置
readonly TEST_TIMEOUT=1200  # 测试超时时间（秒）
readonly SERVER_STARTUP_TIMEOUT=30  # 服务器启动超时时间（秒）
readonly TEST_SERVER_PORT=49012  # 测试服务器端口

# 日志文件路径
readonly LOG_DIR="./logs"
readonly TEST_LOG="$LOG_DIR/test-$(date +%Y%m%d_%H%M%S).log"
readonly COVERAGE_REPORT_DIR="$LOG_DIR/coverage"

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_PURPLE='\033[0;35m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
    echo "[INFO] ${timestamp} - ${message}" >> "$TEST_LOG"
}

# 成功日志
log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
    echo "[SUCCESS] ${timestamp} - ${message}" >> "$TEST_LOG"
}

# 警告日志
log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
    echo "[WARNING] ${timestamp} - ${message}" >> "$TEST_LOG"
}

# 错误日志
log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
    echo "[ERROR] ${timestamp} - ${message}" >> "$TEST_LOG"
}

# 步骤日志
log_step() {
    local step_number="$1"
    local step_name="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_PURPLE}[STEP ${step_number}]${COLOR_NC} ${timestamp} - ${step_name}"
    echo "[STEP ${step_number}] ${timestamp} - ${step_name}" >> "$TEST_LOG"
}

# ==================== 工具函数 ====================

# 初始化日志目录
init_logging() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
    fi
    
    if [ ! -d "$COVERAGE_REPORT_DIR" ]; then
        mkdir -p "$COVERAGE_REPORT_DIR"
    fi
    
    # 创建测试日志文件
    touch "$TEST_LOG"
    log_info "测试日志文件：$TEST_LOG"
}

# 显示脚本头部信息
show_header() {
    echo "=========================================="
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "=========================================="
}

# 检查命令是否存在
check_command() {
    local command="$1"
    local description="$2"
    
    if ! command -v "$command" &> /dev/null; then
        log_error "$description 未安装或未在PATH中：$command"
        return 1
    fi
    return 0
}

# 检查Java 8环境
check_java8_environment() {
    log_step "1" "检查Java 8环境"
    
    # 检查指定的Java 8 JDK是否存在
    if [ -d "$JAVA8_HOME" ] && [ -x "$JAVA8_HOME/bin/java" ]; then
        export JAVA_HOME="$JAVA8_HOME"
        export PATH="$JAVA8_HOME/bin:$PATH"
        log_info "使用指定的Java 8 JDK：$JAVA8_HOME"
    else
        log_warning "指定的Java 8 JDK不存在：$JAVA8_HOME"
        log_info "使用系统默认Java（确保兼容Java 8）"
        
        # 清除JAVA_HOME，使用系统默认
        unset JAVA_HOME
    fi
    
    # 验证Java命令可用性
    if ! check_command "java" "Java运行时"; then
        return 1
    fi
    
    # 获取Java版本信息
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "当前Java版本：$java_version"
    
    # 验证Java版本兼容性
    if [[ "$java_version" =~ ^1\.8\. ]]; then
        log_success "使用Java 8，完全兼容"
    elif [[ "$java_version" =~ ^(11|17|21)\. ]]; then
        log_warning "使用Java $java_version，项目配置为Java 8，但应该向后兼容"
    else
        log_warning "当前Java版本：$java_version，可能存在兼容性问题"
    fi
    
    return 0
}

# 检查Maven环境
check_maven_environment() {
    log_step "2" "检查Maven环境"
    
    # 检查Maven命令
    if ! check_command "mvn" "Apache Maven"; then
        return 1
    fi
    
    # 获取Maven版本信息
    local maven_version=$(mvn -version | head -n 1)
    log_info "Maven版本：$maven_version"
    
    # 配置Maven使用Java 8
    export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxPermSize=512m"
    
    if [ -n "$JAVA_HOME" ]; then
        export MAVEN_OPTS="$MAVEN_OPTS -Djava.home=$JAVA_HOME"
        log_info "Maven配置使用Java：$JAVA_HOME"
    fi
    
    log_info "Maven选项：$MAVEN_OPTS"
    log_success "Maven环境检查完成"
    
    return 0
}

# 清理测试环境
clean_test_environment() {
    log_step "3" "清理测试环境"
    
    # 清理测试数据目录
    local test_data_dirs=("./test-data" "./data" "./file-transfer-demo/data")
    for dir in "${test_data_dirs[@]}"; do
        if [ -d "$dir" ]; then
            rm -rf "$dir"
            log_info "清理测试数据目录：$dir"
        fi
    done
    
    # 清理临时测试文件
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "test-*.dat" -delete 2>/dev/null || true
    find . -name "*.test" -delete 2>/dev/null || true
    
    # 清理之前的测试报告
    for module in "${PROJECT_MODULES[@]}"; do
        if [ -d "$module/target/surefire-reports" ]; then
            rm -rf "$module/target/surefire-reports"
            log_info "清理测试报告目录：$module/target/surefire-reports"
        fi
    done
    
    # 停止可能运行的测试服务器
    local test_server_pids=$(lsof -ti:$TEST_SERVER_PORT 2>/dev/null || true)
    if [ -n "$test_server_pids" ]; then
        log_info "停止占用端口 $TEST_SERVER_PORT 的进程：$test_server_pids"
        kill -9 $test_server_pids 2>/dev/null || true
        sleep 2
    fi
    
    log_success "测试环境清理完成"
    return 0
}

# 编译项目（确保最新代码）
compile_project_for_test() {
    log_step "4" "编译项目（测试前准备）"
    
    local start_time=$(date +%s)
    
    log_info "编译项目以确保测试使用最新代码..."
    
    # 执行Maven编译
    if timeout "$TEST_TIMEOUT" mvn clean compile test-compile -T 1C \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        -Dmaven.compiler.encoding=UTF-8 \
        -Dproject.build.sourceEncoding=UTF-8 \
        >> "$TEST_LOG" 2>&1; then
        
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "项目编译成功，耗时：${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_error "项目编译失败，耗时：${duration}秒"
        log_error "详细错误信息请查看日志文件：$TEST_LOG"
        return 1
    fi
}

# 运行单元测试
run_unit_tests() {
    log_step "5" "运行单元测试"

    local total_modules=0
    local success_modules=0
    local start_time=$(date +%s)

    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            log_warning "模块目录不存在，跳过：$module"
            continue
        fi

        total_modules=$((total_modules + 1))

        log_info "运行模块单元测试：$module"

        # 运行单元测试，排除集成测试
        if timeout "$TEST_TIMEOUT" mvn test -pl "$module" \
            -Dmaven.compiler.source=1.8 \
            -Dmaven.compiler.target=1.8 \
            -Dtest='!**/*IntegrationTest,!**/*EndToEndTest' \
            >> "$TEST_LOG" 2>&1; then

            log_success "模块单元测试通过：$module"
            success_modules=$((success_modules + 1))
        else
            log_error "模块单元测试失败：$module"
        fi
    done

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_info "单元测试结果：$success_modules/$total_modules 个模块通过，耗时：${duration}秒"

    if [ "$success_modules" -eq "$total_modules" ]; then
        log_success "所有单元测试通过"
        return 0
    else
        log_error "部分单元测试失败"
        return 1
    fi
}

# 启动测试服务器
start_test_server() {
    log_info "启动测试服务器（端口：$TEST_SERVER_PORT）..."

    # 确保端口未被占用
    if lsof -ti:$TEST_SERVER_PORT >/dev/null 2>&1; then
        log_error "端口 $TEST_SERVER_PORT 已被占用"
        return 1
    fi

    # 启动Demo服务器作为测试服务器
    mvn spring-boot:run -pl file-transfer-demo \
        -Dspring-boot.run.arguments="--server.port=$TEST_SERVER_PORT --logging.level.com.sdesrd.filetransfer=INFO" \
        >> "$TEST_LOG" 2>&1 &

    local server_pid=$!
    echo $server_pid > "$LOG_DIR/test-server.pid"

    # 等待服务器启动
    log_info "等待测试服务器启动（PID：$server_pid）..."
    local wait_count=0
    while [ $wait_count -lt $SERVER_STARTUP_TIMEOUT ]; do
        if curl -s "http://localhost:$TEST_SERVER_PORT/api/file/health" >/dev/null 2>&1; then
            log_success "测试服务器启动成功"
            return 0
        fi

        sleep 1
        wait_count=$((wait_count + 1))

        # 检查进程是否还在运行
        if ! kill -0 $server_pid 2>/dev/null; then
            log_error "测试服务器进程意外退出"
            return 1
        fi
    done

    log_error "测试服务器启动超时"
    kill $server_pid 2>/dev/null || true
    return 1
}

# 停止测试服务器
stop_test_server() {
    log_info "停止测试服务器..."

    if [ -f "$LOG_DIR/test-server.pid" ]; then
        local server_pid=$(cat "$LOG_DIR/test-server.pid")
        if kill -0 $server_pid 2>/dev/null; then
            kill $server_pid 2>/dev/null || true
            sleep 3

            # 强制杀死如果还在运行
            if kill -0 $server_pid 2>/dev/null; then
                kill -9 $server_pid 2>/dev/null || true
            fi

            log_info "测试服务器已停止（PID：$server_pid）"
        fi
        rm -f "$LOG_DIR/test-server.pid"
    fi

    # 确保端口释放
    local remaining_pids=$(lsof -ti:$TEST_SERVER_PORT 2>/dev/null || true)
    if [ -n "$remaining_pids" ]; then
        log_warning "强制停止占用端口 $TEST_SERVER_PORT 的进程：$remaining_pids"
        kill -9 $remaining_pids 2>/dev/null || true
    fi
}

# 运行集成测试
run_integration_tests() {
    log_step "6" "运行集成测试"

    local start_time=$(date +%s)

    # 启动测试服务器
    if ! start_test_server; then
        log_error "无法启动测试服务器，跳过集成测试"
        return 1
    fi

    # 运行端到端测试
    log_info "运行端到端测试..."
    local integration_success=true

    if timeout "$TEST_TIMEOUT" mvn test -pl file-transfer-demo \
        -Dtest=EndToEndTransferTest \
        -Dserver.port=$TEST_SERVER_PORT \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        >> "$TEST_LOG" 2>&1; then

        log_success "端到端测试通过"
    else
        log_error "端到端测试失败"
        integration_success=false
    fi

    # 停止测试服务器
    stop_test_server

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_info "集成测试耗时：${duration}秒"

    if [ "$integration_success" = true ]; then
        log_success "集成测试通过"
        return 0
    else
        log_error "集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    log_step "7" "运行性能测试"

    local start_time=$(date +%s)

    log_info "运行性能测试..."

    # 运行服务端SDK性能测试
    if timeout "$TEST_TIMEOUT" mvn test -pl file-transfer-server-sdk \
        -Dtest=FileTransferPerformanceTest \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        >> "$TEST_LOG" 2>&1; then

        log_success "性能测试通过"
        local performance_success=true
    else
        log_warning "性能测试未通过或未达到预期性能指标"
        local performance_success=false
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_info "性能测试耗时：${duration}秒"

    if [ "$performance_success" = true ]; then
        log_success "性能测试通过"
        return 0
    else
        log_warning "性能测试未完全通过，但不影响整体测试结果"
        return 1
    fi
}

# 生成测试覆盖率报告
generate_coverage_report() {
    log_step "8" "生成测试覆盖率报告"

    log_info "生成JaCoCo测试覆盖率报告..."

    # 尝试生成覆盖率报告
    if mvn jacoco:report -q >> "$TEST_LOG" 2>&1; then
        log_success "覆盖率报告生成成功"

        # 复制覆盖率报告到统一目录
        for module in "${PROJECT_MODULES[@]}"; do
            if [ -d "$module/target/site/jacoco" ]; then
                local module_coverage_dir="$COVERAGE_REPORT_DIR/$module"
                mkdir -p "$module_coverage_dir"
                cp -r "$module/target/site/jacoco/"* "$module_coverage_dir/" 2>/dev/null || true
                log_info "覆盖率报告已复制：$module_coverage_dir"
            fi
        done

        return 0
    else
        log_warning "无法生成覆盖率报告（可能未配置JaCoCo插件）"
        return 1
    fi
}

# 收集测试结果
collect_test_results() {
    log_step "9" "收集测试结果"

    local total_tests=0
    local failed_tests=0
    local skipped_tests=0

    # 收集各模块的测试结果
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module/target/surefire-reports" ]; then
            continue
        fi

        # 统计测试文件数量
        local module_test_files=$(find "$module/target/surefire-reports" -name "TEST-*.xml" | wc -l)

        if [ "$module_test_files" -gt 0 ]; then
            # 解析测试结果XML文件
            local module_tests=0
            local module_failures=0
            local module_errors=0
            local module_skipped=0

            for xml_file in "$module/target/surefire-reports/TEST-"*.xml; do
                if [ -f "$xml_file" ]; then
                    # 使用grep和awk解析XML属性
                    local test_info=$(grep '<testsuite' "$xml_file" | head -n 1)
                    if [ -n "$test_info" ]; then
                        local tests=$(echo "$test_info" | sed -n 's/.*tests="\([^"]*\)".*/\1/p')
                        local failures=$(echo "$test_info" | sed -n 's/.*failures="\([^"]*\)".*/\1/p')
                        local errors=$(echo "$test_info" | sed -n 's/.*errors="\([^"]*\)".*/\1/p')
                        local skipped=$(echo "$test_info" | sed -n 's/.*skipped="\([^"]*\)".*/\1/p')

                        module_tests=$((module_tests + ${tests:-0}))
                        module_failures=$((module_failures + ${failures:-0}))
                        module_errors=$((module_errors + ${errors:-0}))
                        module_skipped=$((module_skipped + ${skipped:-0}))
                    fi
                fi
            done

            local module_failed=$((module_failures + module_errors))

            total_tests=$((total_tests + module_tests))
            failed_tests=$((failed_tests + module_failed))
            skipped_tests=$((skipped_tests + module_skipped))

            log_info "$module: $module_tests 个测试，$module_failed 个失败，$module_skipped 个跳过"
        else
            log_warning "$module: 未找到测试结果文件"
        fi
    done

    local passed_tests=$((total_tests - failed_tests - skipped_tests))

    # 输出测试结果汇总
    echo ""
    echo "=========================================="
    echo "           测试结果汇总"
    echo "=========================================="
    echo "总测试数: $total_tests"
    echo "通过测试: $passed_tests"
    echo "失败测试: $failed_tests"
    echo "跳过测试: $skipped_tests"

    if [ "$failed_tests" -eq 0 ]; then
        echo -e "测试结果: ${COLOR_GREEN}全部通过${COLOR_NC}"
        local test_success=true
    else
        echo -e "测试结果: ${COLOR_RED}有失败${COLOR_NC}"
        local test_success=false
    fi
    echo "=========================================="

    # 返回测试结果
    if [ "$test_success" = true ]; then
        return 0
    else
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    log_step "10" "生成测试报告"

    local report_file="$LOG_DIR/test-report-$(date +%Y%m%d_%H%M%S).txt"

    {
        echo "=========================================="
        echo "        文件传输SDK测试报告"
        echo "=========================================="
        echo "测试时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "脚本版本：$SCRIPT_VERSION"
        echo "Java版本：$(java -version 2>&1 | head -n 1)"
        echo "Maven版本：$(mvn -version | head -n 1)"
        echo ""

        echo "测试模块："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ -d "$module" ]; then
                echo "  ✓ $module"
            else
                echo "  ✗ $module (目录不存在)"
            fi
        done
        echo ""

        echo "测试结果详情："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ ! -d "$module/target/surefire-reports" ]; then
                echo "  $module: 未运行测试"
                continue
            fi

            local test_files=$(find "$module/target/surefire-reports" -name "TEST-*.xml" | wc -l)
            if [ "$test_files" -gt 0 ]; then
                echo "  $module: $test_files 个测试套件"

                # 列出具体的测试类
                find "$module/target/surefire-reports" -name "TEST-*.xml" | while read xml_file; do
                    local test_class=$(basename "$xml_file" .xml | sed 's/^TEST-//')
                    local test_info=$(grep '<testsuite' "$xml_file" | head -n 1)
                    if [ -n "$test_info" ]; then
                        local tests=$(echo "$test_info" | sed -n 's/.*tests="\([^"]*\)".*/\1/p')
                        local failures=$(echo "$test_info" | sed -n 's/.*failures="\([^"]*\)".*/\1/p')
                        local errors=$(echo "$test_info" | sed -n 's/.*errors="\([^"]*\)".*/\1/p')
                        echo "    - $test_class: ${tests:-0} 测试，$((${failures:-0} + ${errors:-0})) 失败"
                    fi
                done
            else
                echo "  $module: 无测试结果"
            fi
        done
        echo ""

        echo "覆盖率报告："
        if [ -d "$COVERAGE_REPORT_DIR" ]; then
            for module in "${PROJECT_MODULES[@]}"; do
                if [ -d "$COVERAGE_REPORT_DIR/$module" ]; then
                    echo "  $module: $COVERAGE_REPORT_DIR/$module/index.html"
                fi
            done
        else
            echo "  未生成覆盖率报告"
        fi
        echo ""

        echo "测试日志：$TEST_LOG"
        echo "=========================================="

    } > "$report_file"

    log_info "测试报告已生成：$report_file"

    # 显示报告内容
    cat "$report_file"

    return 0
}

# 清理函数（脚本退出时调用）
cleanup_on_exit() {
    local exit_code=$?

    # 停止测试服务器
    stop_test_server

    if [ $exit_code -ne 0 ]; then
        log_error "测试过程中发生错误，退出码：$exit_code"
        log_info "详细错误信息请查看日志文件：$TEST_LOG"
    fi

    # 恢复原始环境变量
    if [ -n "$ORIGINAL_JAVA_HOME" ]; then
        export JAVA_HOME="$ORIGINAL_JAVA_HOME"
    fi

    if [ -n "$ORIGINAL_PATH" ]; then
        export PATH="$ORIGINAL_PATH"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --unit-only       仅运行单元测试"
    echo "  --integration-only 仅运行集成测试"
    echo "  --performance     包含性能测试"
    echo "  --skip-coverage   跳过覆盖率报告生成"
    echo "  --no-cleanup      测试前不清理环境"
    echo "  --no-report       不生成测试报告"
    echo "  --verbose         显示详细输出"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 运行完整测试套件"
    echo "  $0 --unit-only       # 仅运行单元测试"
    echo "  $0 --performance     # 包含性能测试"
    echo "  $0 --verbose         # 显示详细输出"
    echo ""
}

# 主函数
main() {
    # 保存原始环境变量
    export ORIGINAL_JAVA_HOME="$JAVA_HOME"
    export ORIGINAL_PATH="$PATH"

    # 设置退出时清理
    trap cleanup_on_exit EXIT

    # 解析命令行参数
    local unit_only=false
    local integration_only=false
    local run_performance=false
    local skip_coverage=false
    local no_cleanup=false
    local no_report=false
    local verbose=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --unit-only)
                unit_only=true
                integration_only=false
                shift
                ;;
            --integration-only)
                integration_only=true
                unit_only=false
                shift
                ;;
            --performance)
                run_performance=true
                shift
                ;;
            --skip-coverage)
                skip_coverage=true
                shift
                ;;
            --no-cleanup)
                no_cleanup=true
                shift
                ;;
            --no-report)
                no_report=true
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 显示脚本头部信息
    show_header

    # 初始化日志
    init_logging

    # 如果启用详细模式，设置bash调试
    if [ "$verbose" = true ]; then
        set -x
        log_info "启用详细输出模式"
    fi

    # 执行测试步骤
    local test_failed=false

    # 步骤1-4：环境检查和准备
    if ! check_java8_environment; then
        test_failed=true
    elif ! check_maven_environment; then
        test_failed=true
    elif [ "$no_cleanup" = false ] && ! clean_test_environment; then
        test_failed=true
    elif ! compile_project_for_test; then
        test_failed=true
    # 步骤5-7：运行测试
    elif [ "$integration_only" = false ] && ! run_unit_tests; then
        test_failed=true
    elif [ "$unit_only" = false ] && ! run_integration_tests; then
        test_failed=true
    elif [ "$run_performance" = true ] && ! run_performance_tests; then
        log_warning "性能测试未通过，但不影响整体测试结果"
    fi

    # 步骤8-10：生成报告
    if [ "$skip_coverage" = false ]; then
        generate_coverage_report
    fi

    # 收集测试结果
    if ! collect_test_results; then
        test_failed=true
    fi

    # 生成测试报告
    if [ "$no_report" = false ]; then
        generate_test_report
    fi

    # 返回结果
    if [ "$test_failed" = true ]; then
        log_error "测试套件执行失败"
        exit 1
    else
        log_success "测试套件执行成功"
        exit 0
    fi
}

# 执行主函数
main "$@"

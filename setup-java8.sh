#!/bin/bash

# Java 8环境配置脚本
# 功能：配置指定的Java 8 JDK路径，设置Maven环境变量
# 作者：Augment Agent
# 版本：1.0.0

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="Java 8环境配置脚本"

# Java 8 JDK路径配置
readonly JAVA8_HOME="$HOME/.jdks/corretto-1.8.0_452"
readonly JAVA8_BIN="$JAVA8_HOME/bin"

# 备用Java 8路径（常见安装位置）
readonly JAVA8_ALTERNATIVES=(
    "/usr/lib/jvm/java-8-openjdk"
    "/usr/lib/jvm/java-8-oracle"
    "/usr/lib/jvm/java-1.8.0-openjdk"
    "/Library/Java/JavaVirtualMachines/jdk1.8.0_*.jdk/Contents/Home"
    "/opt/java/jdk1.8.0_*"
    "$HOME/.sdkman/candidates/java/8.*"
)

# 环境配置文件
readonly PROFILE_FILES=(
    "$HOME/.bashrc"
    "$HOME/.bash_profile"
    "$HOME/.zshrc"
    "$HOME/.profile"
)

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_PURPLE='\033[0;35m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志
log_info() {
    local message="$1"
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${message}"
}

# 成功日志
log_success() {
    local message="$1"
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${message}"
}

# 警告日志
log_warning() {
    local message="$1"
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${message}"
}

# 错误日志
log_error() {
    local message="$1"
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${message}"
}

# 步骤日志
log_step() {
    local step_number="$1"
    local step_name="$2"
    echo -e "${COLOR_PURPLE}[STEP ${step_number}]${COLOR_NC} ${step_name}"
}

# ==================== 工具函数 ====================

# 显示脚本头部信息
show_header() {
    echo "=========================================="
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "=========================================="
}

# 检查Java版本
check_java_version() {
    local java_path="$1"
    
    if [ ! -x "$java_path" ]; then
        return 1
    fi
    
    local version_output=$("$java_path" -version 2>&1 | head -n 1)
    local version=$(echo "$version_output" | sed -n 's/.*"\(.*\)".*/\1/p')
    
    if [[ "$version" =~ ^1\.8\. ]]; then
        echo "$version"
        return 0
    else
        return 1
    fi
}

# 查找Java 8安装
find_java8_installation() {
    log_step "1" "查找Java 8安装"
    
    # 首先检查指定路径
    if [ -d "$JAVA8_HOME" ] && [ -x "$JAVA8_HOME/bin/java" ]; then
        local version=$(check_java_version "$JAVA8_HOME/bin/java")
        if [ $? -eq 0 ]; then
            log_success "找到指定的Java 8安装：$JAVA8_HOME (版本：$version)"
            echo "$JAVA8_HOME"
            return 0
        else
            log_warning "指定路径存在但不是Java 8：$JAVA8_HOME"
        fi
    else
        log_warning "指定的Java 8路径不存在：$JAVA8_HOME"
    fi
    
    # 检查备用路径
    log_info "搜索备用Java 8安装路径..."
    for alt_path in "${JAVA8_ALTERNATIVES[@]}"; do
        # 处理通配符路径
        for expanded_path in $alt_path; do
            if [ -d "$expanded_path" ] && [ -x "$expanded_path/bin/java" ]; then
                local version=$(check_java_version "$expanded_path/bin/java")
                if [ $? -eq 0 ]; then
                    log_success "找到Java 8安装：$expanded_path (版本：$version)"
                    echo "$expanded_path"
                    return 0
                fi
            fi
        done
    done
    
    # 检查系统PATH中的java
    if command -v java >/dev/null 2>&1; then
        local system_java=$(which java)
        local version=$(check_java_version "$system_java")
        if [ $? -eq 0 ]; then
            # 尝试找到JAVA_HOME
            local java_home=$(dirname "$(dirname "$system_java")")
            log_success "系统PATH中找到Java 8：$java_home (版本：$version)"
            echo "$java_home"
            return 0
        else
            log_warning "系统PATH中的Java不是版本8"
        fi
    fi
    
    log_error "未找到Java 8安装"
    return 1
}

# 验证Java 8环境
validate_java8_environment() {
    log_step "2" "验证Java 8环境"
    
    local java8_path="$1"
    
    if [ -z "$java8_path" ]; then
        log_error "Java 8路径为空"
        return 1
    fi
    
    # 检查关键文件和目录
    local required_paths=(
        "$java8_path/bin/java"
        "$java8_path/bin/javac"
        "$java8_path/lib"
    )
    
    for path in "${required_paths[@]}"; do
        if [ ! -e "$path" ]; then
            log_error "缺少必要文件或目录：$path"
            return 1
        fi
    done
    
    # 验证Java版本
    local java_version=$(check_java_version "$java8_path/bin/java")
    if [ $? -ne 0 ]; then
        log_error "Java版本验证失败"
        return 1
    fi
    
    # 验证javac版本
    local javac_version=$("$java8_path/bin/javac" -version 2>&1)
    if [[ ! "$javac_version" =~ 1\.8\. ]]; then
        log_error "javac版本不是1.8：$javac_version"
        return 1
    fi
    
    log_success "Java 8环境验证通过"
    log_info "Java版本：$java_version"
    log_info "Javac版本：$javac_version"
    
    return 0
}

# 配置环境变量
configure_environment_variables() {
    log_step "3" "配置环境变量"
    
    local java8_path="$1"
    local configure_persistent="$2"
    
    # 设置当前会话的环境变量
    export JAVA_HOME="$java8_path"
    export PATH="$java8_path/bin:$PATH"
    
    # 配置Maven选项
    export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g"
    if [ -n "$JAVA_HOME" ]; then
        export MAVEN_OPTS="$MAVEN_OPTS -Djava.home=$JAVA_HOME"
    fi
    
    log_success "当前会话环境变量已配置"
    log_info "JAVA_HOME=$JAVA_HOME"
    log_info "PATH已更新（Java 8 bin目录优先）"
    log_info "MAVEN_OPTS=$MAVEN_OPTS"
    
    # 如果需要持久化配置
    if [ "$configure_persistent" = true ]; then
        configure_persistent_environment "$java8_path"
    fi
    
    return 0
}

# 配置持久化环境变量
configure_persistent_environment() {
    log_step "4" "配置持久化环境变量"
    
    local java8_path="$1"
    local config_added=false
    
    # 生成环境配置内容
    local env_config="
# Java 8 环境配置 - 由 $SCRIPT_NAME 自动生成
export JAVA_HOME=\"$java8_path\"
export PATH=\"\$JAVA_HOME/bin:\$PATH\"
export MAVEN_OPTS=\"-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -Djava.home=\$JAVA_HOME\"
# Java 8 环境配置结束
"
    
    # 查找合适的配置文件
    for profile_file in "${PROFILE_FILES[@]}"; do
        if [ -f "$profile_file" ] || [ "$profile_file" = "$HOME/.bashrc" ]; then
            # 检查是否已经配置过
            if grep -q "由 $SCRIPT_NAME 自动生成" "$profile_file" 2>/dev/null; then
                log_warning "环境配置已存在于：$profile_file"
                continue
            fi
            
            # 创建备份
            if [ -f "$profile_file" ]; then
                cp "$profile_file" "${profile_file}.backup.$(date +%Y%m%d_%H%M%S)"
                log_info "已备份配置文件：${profile_file}.backup.$(date +%Y%m%d_%H%M%S)"
            fi
            
            # 添加配置
            echo "$env_config" >> "$profile_file"
            log_success "环境配置已添加到：$profile_file"
            config_added=true
            break
        fi
    done
    
    if [ "$config_added" = false ]; then
        # 如果没有找到合适的配置文件，创建.bashrc
        echo "$env_config" >> "$HOME/.bashrc"
        log_success "环境配置已添加到：$HOME/.bashrc"
    fi
    
    log_info "重新加载shell配置以使环境变量生效："
    log_info "  source ~/.bashrc"
    log_info "或重新打开终端"
    
    return 0
}

# 验证配置结果
verify_configuration() {
    log_step "5" "验证配置结果"
    
    # 验证Java命令
    if ! command -v java >/dev/null 2>&1; then
        log_error "java命令不可用"
        return 1
    fi
    
    # 验证Java版本
    local java_version=$(java -version 2>&1 | head -n 1)
    if [[ ! "$java_version" =~ 1\.8\. ]]; then
        log_error "当前Java版本不是1.8：$java_version"
        return 1
    fi
    
    # 验证javac命令
    if ! command -v javac >/dev/null 2>&1; then
        log_error "javac命令不可用"
        return 1
    fi
    
    # 验证Maven（如果可用）
    if command -v mvn >/dev/null 2>&1; then
        local maven_java_home=$(mvn -version | grep "Java home" | cut -d: -f2 | xargs)
        log_info "Maven使用的Java路径：$maven_java_home"
        
        # 验证Maven使用的是正确的Java版本
        if [[ "$maven_java_home" == *"1.8"* ]] || [[ "$maven_java_home" == *"8"* ]]; then
            log_success "Maven配置正确使用Java 8"
        else
            log_warning "Maven可能未使用Java 8，请检查MAVEN_OPTS配置"
        fi
    else
        log_warning "Maven未安装，无法验证Maven配置"
    fi
    
    log_success "配置验证完成"
    log_info "当前Java版本：$java_version"
    log_info "JAVA_HOME：$JAVA_HOME"
    
    return 0
}

# 显示配置信息
show_configuration_info() {
    echo ""
    echo "=========================================="
    echo "        Java 8环境配置信息"
    echo "=========================================="
    echo "JAVA_HOME: $JAVA_HOME"
    echo "Java版本: $(java -version 2>&1 | head -n 1)"
    echo "Javac版本: $(javac -version 2>&1)"
    if command -v mvn >/dev/null 2>&1; then
        echo "Maven版本: $(mvn -version | head -n 1)"
        echo "Maven Java: $(mvn -version | grep "Java home" | cut -d: -f2 | xargs)"
    fi
    echo "PATH: $PATH"
    echo "MAVEN_OPTS: $MAVEN_OPTS"
    echo "=========================================="
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --persistent      配置持久化环境变量（写入shell配置文件）"
    echo "  --verify-only     仅验证当前Java 8配置"
    echo "  --show-config     显示当前配置信息"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "功能说明:"
    echo "  1. 自动查找Java 8安装"
    echo "  2. 配置JAVA_HOME和PATH环境变量"
    echo "  3. 配置Maven使用Java 8"
    echo "  4. 可选择持久化配置到shell配置文件"
    echo ""
    echo "示例:"
    echo "  $0                    # 配置当前会话的Java 8环境"
    echo "  $0 --persistent       # 配置并持久化到shell配置文件"
    echo "  $0 --verify-only      # 仅验证当前Java 8配置"
    echo "  $0 --show-config      # 显示当前配置信息"
    echo ""
}

# 主函数
main() {
    # 解析命令行参数
    local persistent=false
    local verify_only=false
    local show_config=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --persistent)
                persistent=true
                shift
                ;;
            --verify-only)
                verify_only=true
                shift
                ;;
            --show-config)
                show_config=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示脚本头部信息
    show_header
    
    # 如果只是显示配置信息
    if [ "$show_config" = true ]; then
        show_configuration_info
        exit 0
    fi
    
    # 如果只是验证配置
    if [ "$verify_only" = true ]; then
        if verify_configuration; then
            show_configuration_info
            exit 0
        else
            exit 1
        fi
    fi
    
    # 执行完整配置流程
    local java8_path
    
    # 查找Java 8安装
    if ! java8_path=$(find_java8_installation); then
        log_error "无法找到Java 8安装，请手动安装Java 8"
        log_info "推荐安装Amazon Corretto 8：https://aws.amazon.com/corretto/"
        exit 1
    fi
    
    # 验证Java 8环境
    if ! validate_java8_environment "$java8_path"; then
        log_error "Java 8环境验证失败"
        exit 1
    fi
    
    # 配置环境变量
    if ! configure_environment_variables "$java8_path" "$persistent"; then
        log_error "环境变量配置失败"
        exit 1
    fi
    
    # 验证配置结果
    if ! verify_configuration; then
        log_error "配置验证失败"
        exit 1
    fi
    
    # 显示配置信息
    show_configuration_info
    
    log_success "Java 8环境配置完成"
    
    if [ "$persistent" = true ]; then
        log_info "环境变量已持久化，重新打开终端或执行 'source ~/.bashrc' 使配置生效"
    else
        log_info "环境变量仅在当前会话有效，如需持久化请使用 --persistent 选项"
    fi
    
    exit 0
}

# 执行主函数
main "$@"

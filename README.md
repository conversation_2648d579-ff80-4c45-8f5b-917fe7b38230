# 文件传输SDK

一个安全、高效的Java文件传输解决方案，支持用户认证、断点续传和差异化限速，包含服务端SDK和客户端SDK。

## 项目特性

- ✅ **用户认证**: 基于HMAC-SHA256的安全认证机制
- ✅ **多用户隔离**: 每个用户独立的存储空间和配置
- ✅ **差异化限速**: 不同用户不同的传输速度配额
- ✅ **断点续传**: 支持文件上传下载中断后继续传输
- ✅ **分块传输**: 大文件自动分块，提高传输可靠性
- ✅ **秒传功能**: 基于MD5去重，避免重复上传
- ✅ **进度监听**: 实时获取传输进度和状态
- ✅ **并发控制**: 支持多文件同时传输
- ✅ **文件校验**: MD5校验确保文件完整性
- ✅ **嵌入式数据库**: 使用SQLite，无需额外数据库配置
- ✅ **Spring Boot集成**: 支持自动配置和依赖注入
- ✅ **异步处理**: 专用线程池优化I/O性能
- ✅ **智能监控**: 自动监控传输状态和系统健康
- ✅ **管理接口**: 提供系统统计和管理功能
- ✅ **测试工具**: 内置测试工具简化开发调试

## 项目结构

```
file-transfer-sdk/
├── pom.xml                        # 父项目配置
├── file-transfer-server-sdk/      # 服务端SDK
│   ├── controller/                # REST API控制器
│   ├── service/                   # 业务逻辑服务
│   ├── config/                    # 配置类
│   ├── util/                      # 工具类
│   └── resources/sql/             # 数据库脚本
├── file-transfer-client-sdk/      # 客户端SDK
├── file-transfer-demo/            # 使用示例(客户端、服务端)
├── file-transfer-server-standalone/ # 独立服务端构建示例
└── README.md                      # 项目说明
```

## 快速开始

### 1. 编译项目

```bash
cd file-transfer-sdk
mvn clean install
```

### 2. 运行Demo

```bash
cd file-transfer-demo
mvn spring-boot:run
```

访问 http://localhost:49011/doc.html 查看API文档

### 3. 使用服务端SDK

#### 添加依赖

```xml
<dependency>
    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-server-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 配置文件

```yaml
# application.yml
spring:
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: ********************************************

file:
  transfer:
    server:
      enabled: true
      database-path: ./data/file-transfer/database.db
      
      # 清理配置
      cleanup-interval: 3600000        # 1小时（毫秒）
      record-expire-time: 86400000     # 24小时（毫秒）
      
      # 默认配置（兜底配置）
      default:
        storage-path: ./data/file-transfer/files
        upload-rate-limit: 10485760  # 10MB/s
        download-rate-limit: 10485760  # 10MB/s
        default-chunk-size: 2097152  # 2MB
        max-file-size: 104857600  # 100MB
        max-in-memory-size: 10485760  # 10MB
        fast-upload-enabled: true
        rate-limit-enabled: true
      
      # 用户配置（必需）
      users:
        # 基础用户
        user1:
          secret-key: "user1-secret-2024"
          storage-path: ./data/users/user1
          upload-rate-limit: 2097152    # 2MB/s
          download-rate-limit: 2097152
          max-file-size: 52428800       # 50MB
          
        # VIP用户
        vip:
          secret-key: "vip-secret-2024"
          storage-path: ./data/users/vip
          upload-rate-limit: 10485760   # 10MB/s
          download-rate-limit: 10485760
          max-file-size: 209715200      # 200MB
          
        # 管理员
        admin:
          secret-key: "admin-secret-2024"
          storage-path: ./data/admin
          upload-rate-limit: 52428800   # 50MB/s
          download-rate-limit: 52428800
          max-file-size: **********     # 1GB
          rate-limit-enabled: false     # 不限速
```

#### 启动类

```java
@SpringBootApplication
public class YourApplication {
    public static void main(String[] args) {
        SpringApplication.run(YourApplication.class, args);
    }
}
```

### 4. 使用客户端SDK

#### 添加依赖

```xml
<dependency>
    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-client-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 创建客户端

```java
// 配置客户端认证信息（必填）
ClientConfig config = new ClientConfig();
config.getAuth().setServerAddr("your-domain.com");
config.getAuth().setServerPort(49011);
config.getAuth().setUser("user1");  // 与服务端配置的用户名一致
config.getAuth().setSecretKey("user1-secret-2024");  // 与服务端对应用户的密钥一致

// 配置传输参数
config.setChunkSize(2 * 1024 * 1024); // 2MB 分块
config.setMaxConcurrentTransfers(3);

// 创建客户端
FileTransferClient client = new FileTransferClient(config);
```

#### 上传文件

```java
// 创建传输监听器
TransferListener listener = new TransferListener() {
    @Override
    public void onProgress(TransferProgress progress) {
        System.out.printf("上传进度: %.2f%% (%s/%s)%n", 
            progress.getProgress(),
            FileUtils.formatFileSize(progress.getTransferredSize()),
            FileUtils.formatFileSize(progress.getTotalSize()));
    }
    
    @Override
    public void onCompleted(TransferProgress progress) {
        System.out.println("上传完成: " + progress.getFileName());
    }
};

// 异步上传文件
CompletableFuture<UploadResult> future = client.uploadFile(
    "/path/to/local/file.txt", 
    "remote-file.txt", 
    listener
);

// 等待上传完成
UploadResult result = future.get();
if (result.isSuccess()) {
    System.out.println("文件ID: " + result.getFileId());
}
```

#### 下载文件

```java
// 异步下载文件
CompletableFuture<DownloadResult> future = client.downloadFile(
    "file-id", 
    "/path/to/save/file.txt", 
    listener
);

// 等待下载完成
DownloadResult result = future.get();
if (result.isSuccess()) {
    System.out.println("保存路径: " + result.getLocalPath());
}
```

#### 查询传输进度

```java
TransferProgress progress = client.queryProgress("transfer-id");
System.out.printf("进度: %.2f%%, 状态: %s%n", 
    progress.getProgress(), 
    progress.isCompleted() ? "完成" : "传输中");
```

## API接口

### 文件传输接口

所有传输接口都需要在HTTP请求头中提供认证信息：
- `X-File-Transfer-User`: 用户名
- `X-File-Transfer-Auth`: 认证令牌（Base64编码的时间戳和HMAC-SHA256签名）

| 方法 | 路径 | 功能 | 认证 |
|------|------|------|------|
| `POST` | `/api/file/upload/init` | 初始化文件上传 | 🔒 必需 |
| `POST` | `/api/file/upload/chunk` | 上传文件分块 | 🔒 必需 |
| `POST` | `/api/file/upload/complete/{transferId}` | 完成文件上传 | 🔒 必需 |
| `GET` | `/api/file/download/{fileId}` | 下载文件 | 🔒 必需 |
| `GET` | `/api/file/progress/{transferId}` | 查询传输进度 | 🔒 必需 |
| `GET` | `/api/file/health` | 健康检查 | ✅ 无需 |

### 管理接口

用于系统监控和管理的接口（生产环境建议通过网络层面限制访问）：

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| `GET` | `/api/admin/statistics` | 获取传输统计信息 | 返回各状态传输数量和成功率 |
| `GET` | `/api/admin/health` | 系统健康检查（详细） | 包含JVM信息和传输统计 |
| `GET` | `/api/admin/clear-rate-limiters` | 清理限流器缓存 | 清理所有用户的限流器状态 |

#### 统计信息示例

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "pendingCount": 0,
    "transferringCount": 5,
    "completedCount": 100,
    "failedCount": 2,
    "successRate": 98.04
  }
}
```

#### 系统健康信息示例

```json
{
  "code": 200,
  "message": "操作成功", 
  "data": {
    "status": "UP",
    "timestamp": 1642579200000,
    "totalMemory": **********,
    "freeMemory": **********,
    "usedMemory": **********,
    "maxMemory": **********,
    "transferStats": {
      "pendingCount": 0,
      "transferringCount": 5,
      "completedCount": 100,
      "failedCount": 2,
      "successRate": 98.04
    }
  }
}
```

## 监控功能

### 自动监控

系统内置了完整的监控功能：

- **传输状态监控**：每5分钟统计各状态传输数量
- **过期记录清理**：每1小时清理过期传输记录（可配置）
- **卡住传输检测**：检测2小时无进展的传输
- **失败率告警**：失败率超过10%时输出警告日志

### 监控日志示例

```
2024-01-15 10:30:00 [FileTransfer-Monitor] INFO  - 传输状态统计 - 待传输: 0, 传输中: 5, 已完成: 100, 已失败: 2
2024-01-15 10:30:00 [FileTransfer-Monitor] WARN  - 传输失败率过高: 10.50% (21/200)
2024-01-15 11:00:00 [FileTransfer-Monitor] INFO  - 开始清理过期传输记录，数量: 15
2024-01-15 11:00:00 [FileTransfer-Monitor] WARN  - 发现卡住的传输 - 数量: 2
```

## 性能特性

### 异步处理优化

- **文件传输线程池**：核心5线程，最大20线程，队列容量100
- **文件I/O线程池**：核心10线程，最大50线程，队列容量200
- **智能拒绝策略**：队列满时由调用线程执行，避免任务丢失
- **优雅关闭**：应用关闭时等待30秒让任务完成

### 限流机制

- **用户级限流**：每个用户独立的上传下载限速
- **基于Guava RateLimiter**：令牌桶算法，平滑限流
- **动态调整**：运行时可清理限流器缓存重新生效配置

### 文件处理优化

- **分块传输**：大文件自动分块，默认2MB
- **断点续传**：网络中断后从断点继续传输
- **MD5秒传**：重复文件智能检测，避免重复传输
- **内存控制**：分块处理避免大文件占用过多内存

## 测试工具

SDK内置了测试工具类，方便开发和调试：

### 创建测试文件

```java
import com.sdesrd.filetransfer.server.util.FileTransferTestUtils;

// 创建不同大小的测试文件
File smallFile = FileTransferTestUtils.createSmallTestFile("./test/small.dat");   // 1MB
File mediumFile = FileTransferTestUtils.createMediumTestFile("./test/medium.dat"); // 10MB
File largeFile = FileTransferTestUtils.createLargeTestFile("./test/large.dat");   // 100MB

// 创建自定义大小的测试文件
File customFile = FileTransferTestUtils.createTestFile("./test/custom.dat", 50 * 1024 * 1024); // 50MB
```

### 文件验证

```java
// 验证两个文件是否相同（通过MD5比较）
boolean isEqual = FileTransferTestUtils.verifyFilesEqual(
    "./original/file.dat", 
    "./downloaded/file.dat"
);

if (isEqual) {
    System.out.println("文件传输成功，内容一致！");
}
```

### 清理测试环境

```java
// 清理测试目录
FileTransferTestUtils.cleanupTestDirectory("./test/");

// 删除单个测试文件
FileTransferTestUtils.deleteTestFile("./test/large.dat");
```

## 配置参数

### 服务端配置

#### 全局配置
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `file.transfer.server.enabled` | 是否启用服务 | `true` |
| `file.transfer.server.database-path` | 数据库文件路径 | `./data/file-transfer/database.db` |
| `file.transfer.server.cleanup-interval` | 清理间隔（毫秒） | `3600000` (1小时) |
| `file.transfer.server.record-expire-time` | 记录过期时间（毫秒） | `86400000` (24小时) |
| `file.transfer.server.swagger-enabled` | 是否启用API文档 | `true` |
| `file.transfer.server.cors-enabled` | 是否启用跨域 | `true` |

#### 默认配置 (default节点)
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `storage-path` | 默认文件存储路径 | `./data/file-transfer/files` |
| `upload-rate-limit` | 默认上传限速(字节/秒) | `10485760` (10MB/s) |
| `download-rate-limit` | 默认下载限速(字节/秒) | `10485760` (10MB/s) |
| `default-chunk-size` | 默认分块大小 | `2097152` (2MB) |
| `max-file-size` | 默认最大文件大小 | `104857600` (100MB) |
| `max-in-memory-size` | 默认最大内存大小 | `10485760` (10MB) |
| `fast-upload-enabled` | 是否启用秒传 | `true` |
| `rate-limit-enabled` | 是否启用限速 | `true` |

#### 用户配置 (users节点)
| 参数 | 说明 | 是否必填 |
|------|------|--------|
| `secret-key` | 用户密钥 | ✅ 必填 |
| `storage-path` | 用户存储路径 | 可选 |
| `upload-rate-limit` | 用户上传限速 | 可选 |
| `download-rate-limit` | 用户下载限速 | 可选 |
| `default-chunk-size` | 用户分块大小 | 可选 |
| `max-file-size` | 用户最大文件大小 | 可选 |
| `max-in-memory-size` | 用户最大内存大小 | 可选 |
| `fast-upload-enabled` | 用户是否启用秒传 | 可选 |
| `rate-limit-enabled` | 用户是否启用限速 | 可选 |

### 客户端配置

#### 认证配置 (必填)
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `auth.serverAddr` | 服务器地址 | `localhost` |
| `auth.serverPort` | 服务器端口 | `49011` |
| `auth.user` | 用户名 | ❌ 必填 |
| `auth.secretKey` | 用户密钥 | ❌ 必填 |

#### 传输配置 (可选)
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `chunkSize` | 分块大小 | `2097152` (2MB) |
| `maxConcurrentTransfers` | 最大并发传输数 | `3` |
| `connectTimeoutSeconds` | 连接超时时间 | `30` |
| `readTimeoutSeconds` | 读取超时时间 | `60` |
| `retryCount` | 重试次数 | `3` |

## 认证机制

- **认证方式**: HMAC-SHA256签名
- **令牌格式**: Base64(timestamp:signature)
- **签名数据**: username:timestamp
- **有效期**: 5分钟
- **防重放**: 基于时间戳

## 数据库设计

### 文件传输记录表 (file_transfer_record)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键ID |
| file_id | TEXT | 文件标识符(MD5) |
| file_name | TEXT | 原始文件名 |
| file_size | INTEGER | 文件大小 |
| status | INTEGER | 传输状态(0-待传输,1-传输中,2-完成,3-失败) |

### 文件分块记录表 (file_chunk_record)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键ID |
| transfer_id | TEXT | 传输记录ID |
| chunk_index | INTEGER | 分块序号 |
| chunk_size | INTEGER | 分块大小 |
| status | INTEGER | 分块状态 |

## 技术栈

- **Java 8+** - 基础运行环境
- **Spring Boot 2.6.6** - 应用框架
- **MyBatis Plus 3.5.6** - ORM框架
- **SQLite 3.42.0** - 嵌入式数据库
- **OkHttp 4.9.3** - HTTP客户端
- **Guava 29.0** - 限流工具
- **Hutool 5.3.8** - 工具类库
- **Lombok 1.18.38** - 代码简化

## 部署指南

### 生产环境部署

#### 1. 安全配置

```yaml
# 生产环境配置建议
file:
  transfer:
    server:
      # 通过网络层面限制管理接口访问
      # 如：使用nginx反向代理，禁止外网访问 /api/admin/*
      
      # 设置合理的过期时间
      cleanup-interval: 1800000     # 30分钟清理一次
      record-expire-time: 259200000 # 3天过期
      
      users:
        # 使用强密钥
        prod-user:
          secret-key: "prod-very-long-and-complex-secret-key-2024"
          # 其他配置...
```

#### 2. 性能调优

```yaml
# 根据服务器配置调整线程池大小
# 可通过 AsyncConfig 调整以下参数：
# - 文件传输线程池：核心线程数、最大线程数
# - 文件I/O线程池：核心线程数、最大线程数
# - 队列容量

# 根据网络环境调整分块大小
file:
  transfer:
    server:
      default:
        default-chunk-size: 4194304  # 4MB（高带宽环境）
        # 或
        default-chunk-size: 1048576  # 1MB（低带宽环境）
```

#### 3. 监控配置

```yaml
# 日志配置
logging:
  level:
    com.sdesrd.filetransfer: INFO  # 生产环境使用INFO级别
    com.sdesrd.filetransfer.server.service.FileTransferMonitorService: DEBUG  # 监控日志保持DEBUG
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/file-transfer.log
    max-size: 100MB
    max-history: 30
```

#### 4. 系统监控

- 通过 `/api/admin/health` 接口监控系统状态
- 设置失败率阈值告警（当前为10%）
- 监控磁盘空间，避免存储文件过多
- 定期备份SQLite数据库文件

### Docker部署

```dockerfile
FROM openjdk:8-jre-alpine

VOLUME /data

COPY target/your-app.jar app.jar

EXPOSE 49011

ENV JAVA_OPTS=""
ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app.jar" ]
```

## 最佳实践

### 1. 项目集成

在现有Spring Boot项目中集成服务端SDK：

```java
@SpringBootApplication
@ComponentScan(basePackages = {"com.your.package", "com.sdesrd.filetransfer.server"})
public class YourApplication {
    // ...
}
```

### 2. 自定义用户配置

通过配置文件为不同用户设置不同的权限和限制：

```yaml
file:
  transfer:
    server:
      users:
        # 试用用户 - 严格限制
        trial:
          secret-key: "trial-secret-2024"
          storage-path: ./data/trial
          upload-rate-limit: 524288      # 512KB/s
          download-rate-limit: 524288
          max-file-size: 5242880         # 5MB
          
        # 普通用户 - 标准配置
        standard:
          secret-key: "standard-secret-2024"
          storage-path: ./data/standard
          upload-rate-limit: 2097152     # 2MB/s
          download-rate-limit: 2097152
          max-file-size: 52428800        # 50MB
          
        # 企业用户 - 高级配置
        enterprise:
          secret-key: "enterprise-secret-2024"
          storage-path: ./data/enterprise
          upload-rate-limit: 20971520    # 20MB/s
          download-rate-limit: 20971520
          max-file-size: **********      # 1GB
```

### 3. 错误处理

```java
try {
    UploadResult result = client.uploadFileSync(filePath, null, null);
    if (!result.isSuccess()) {
        log.error("上传失败: {}", result.getErrorMessage());
    }
} catch (FileTransferException e) {
    log.error("文件传输异常", e);
} catch (Exception e) {
    log.error("其他异常", e);
}
```

### 4. 资源管理

```java
// 使用try-with-resources确保资源释放
try (FileTransferClient client = new FileTransferClient(config)) {
    // 执行文件传输操作
    client.uploadFileSync(filePath, null, listener);
}
```

### 5. 性能测试

```java
// 使用内置测试工具进行性能测试
public void performanceTest() {
    try {
        // 创建测试文件
        File testFile = FileTransferTestUtils.createLargeTestFile("./test/perf-test.dat");
        
        long startTime = System.currentTimeMillis();
        
        // 执行上传测试
        UploadResult result = client.uploadFileSync(testFile.getPath(), null, null);
        
        long duration = System.currentTimeMillis() - startTime;
        double speed = (double) testFile.length() / duration * 1000; // bytes/s
        
        System.out.printf("上传完成 - 大小: %s, 耗时: %dms, 速度: %s/s%n",
                FileUtils.formatFileSize(testFile.length()),
                duration,
                FileUtils.formatFileSize((long) speed));
                
        // 清理测试文件
        FileTransferTestUtils.deleteTestFile(testFile.getPath());
        
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

## 故障排除

### 常见问题

#### Q: 如何配置用户认证？
A: 在服务端配置文件的 `users` 节点下添加用户，设置 `secret-key`。客户端使用对应的用户名和密钥进行认证。

#### Q: 如何为不同用户设置不同的传输限速？
A: 在用户配置中分别设置 `upload-rate-limit` 和 `download-rate-limit`。未配置的用户将使用默认配置。

#### Q: 如何修改用户的文件存储路径？
A: 在用户配置中设置 `storage-path` 参数，每个用户可以有独立的存储目录。

#### Q: 客户端认证失败怎么办？
A: 检查以下几点：
- 用户名是否在服务端配置中存在
- 密钥是否与服务端配置一致
- 网络连接是否正常
- 认证令牌是否过期（有效期5分钟）

#### Q: 传输失败率过高怎么办？
A: 
1. 检查网络连接稳定性
2. 调整重试次数和超时时间
3. 适当减小分块大小
4. 检查服务端日志确认具体错误原因
5. 通过 `/api/admin/statistics` 监控传输统计

#### Q: 如何监控系统性能？
A: 
1. 使用 `/api/admin/health` 查看系统状态
2. 观察监控日志输出
3. 监控JVM内存使用情况
4. 定期检查磁盘空间

#### Q: 数据库表是否自动创建？
A: 是的，应用启动时会自动检查并创建必要的表结构。

#### Q: 是否支持集群部署？
A: 当前版本使用SQLite，建议单机部署。集群支持需要改用MySQL等数据库。

#### Q: 如何保证认证密钥的安全性？
A: 建议：
- 使用强随机字符串作为密钥，至少16位
- 不同用户使用不同的密钥
- 定期更换密钥
- 生产环境使用HTTPS
- 密钥不要硬编码在代码中，使用配置文件或环境变量

## 注意事项

1. **用户认证**: 所有操作都需要提供有效的用户名和密钥，缺少认证信息会返回401错误
2. **密钥安全**: 密钥泄露会导致安全风险，请妥善保管并定期更换
3. **用户隔离**: 每个用户只能访问自己的文件，无法跨用户访问
4. **文件大小限制**: 不同用户可以有不同的文件大小限制，可通过配置调整
5. **传输限速**: 每个用户有独立的速度限制，超出限制会被限流
6. **数据库位置**: SQLite文件位置需要有读写权限
7. **并发限制**: 合理设置并发数，避免资源耗尽
8. **认证令牌**: 令牌有效期5分钟，过期需要重新生成
9. **错误重试**: 网络不稳定时适当增加重试次数
10. **日志级别**: 生产环境建议调整为INFO级别
11. **管理接口安全**: 生产环境应通过网络层面限制 `/api/admin/*` 接口访问
12. **监控告警**: 建议接入外部监控系统，及时发现异常
13. **磁盘空间**: 定期清理过期文件，避免磁盘空间耗尽
14. **性能调优**: 根据实际硬件配置调整线程池大小和分块大小

package com.sdesrd.filetransfer.client;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.FileUtils;

/**
 * 文件传输客户端单元测试
 * 使用Mock服务端进行测试，不依赖实际的服务端实例
 */
@DisplayName("文件传输客户端单元测试")
class FileTransferClientIntegrationTest {
    
    @TempDir
    Path tempDir;
    
    private FileTransferClient client;
    private File testFile;
    private TestTransferListener listener;
    
    @BeforeEach
    void setUp() throws IOException {
        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.localConfig("demo", "demo-secret-key-2024");
        config.setChunkSize(1024 * 1024); // 1MB 分块，便于测试
        config.setMaxConcurrentTransfers(2);
        config.setRetryCount(3);
        
        client = new FileTransferClient(config);
        
        // 创建测试文件
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            content.append("这是一个测试文件内容，用于验证文件传输功能。\n");
        }
        testFile = createTestFile("test-file.txt", content.toString());
        
        // 创建测试监听器
        listener = new TestTransferListener();
    }
    
    @AfterEach
    void tearDown() {
        if (client != null) {
            client.close();
        }
        
        // 清理测试文件
        if (testFile != null && testFile.exists()) {
            testFile.delete();
        }
    }
    
    @Test
    @DisplayName("客户端配置测试")
    void testClientConfiguration() {
        // 验证客户端配置
        assertNotNull(client);

        // 测试文件存在性验证
        assertTrue(testFile.exists());
        assertTrue(testFile.length() > 0);

        // 验证监听器初始状态
        assertFalse(listener.isStartCalled());
        assertFalse(listener.isProgressCalled());
        assertFalse(listener.isCompletedCalled());
        assertFalse(listener.isErrorCalled());
    }
    
    @Test
    @DisplayName("文件不存在异常测试")
    void testFileNotExistsException() {
        // 尝试上传不存在的文件
        assertThrows(FileTransferException.class, () -> {
            client.uploadFileSync("/path/to/nonexistent/file.txt", null, null);
        });
    }
    
    @Test
    @DisplayName("配置验证测试")
    void testConfigValidation() {
        // 测试无效配置
        assertThrows(IllegalStateException.class, () -> {
            ClientConfig invalidConfig = new ClientConfig();
            invalidConfig.getAuth().setUser(""); // 空用户名
            invalidConfig.getAuth().validate();
        });

        assertThrows(IllegalStateException.class, () -> {
            ClientConfig invalidConfig = new ClientConfig();
            invalidConfig.getAuth().setUser("test");
            invalidConfig.getAuth().setSecretKey(""); // 空密钥
            invalidConfig.getAuth().validate();
        });
    }
    
    @Test
    @DisplayName("文件MD5计算测试")
    void testFileMD5Calculation() throws Exception {
        // 计算测试文件的MD5
        String md5 = FileUtils.calculateMD5(testFile);

        // 验证MD5格式
        assertNotNull(md5);
        assertEquals(32, md5.length()); // MD5应该是32位十六进制字符串
        assertTrue(md5.matches("[a-fA-F0-9]{32}")); // 验证是否为有效的十六进制
    }
    
    @Test
    @DisplayName("客户端资源管理测试")
    void testClientResourceManagement() {
        // 测试客户端可以正常关闭
        assertDoesNotThrow(() -> {
            client.close();
        });

        // 重新创建客户端用于后续测试
        ClientConfig config = ClientConfigBuilder.localConfig("demo", "demo-secret-key-2024");
        config.setChunkSize(1024 * 1024);
        config.setMaxConcurrentTransfers(2);
        config.setRetryCount(3);

        client = new FileTransferClient(config);
        assertNotNull(client);
    }
    
    @Test
    @DisplayName("大文件创建和MD5计算测试")
    void testLargeFileHandling() throws Exception {
        // 创建较大的测试文件（1MB）
        StringBuilder largeContent = new StringBuilder();
        String chunk = "";
        for (int i = 0; i < 1024; i++) {
            chunk += "X";
        }
        for (int i = 0; i < 1024; i++) {
            largeContent.append(chunk);
        }
        File largeFile = createTestFile("large-test-file.dat", largeContent.toString()); // 1MB

        try {
            // 验证文件创建成功
            assertTrue(largeFile.exists());
            assertEquals(1024 * 1024, largeFile.length()); // 1MB

            // 计算MD5
            String md5 = FileUtils.calculateMD5(largeFile);
            assertNotNull(md5);
            assertEquals(32, md5.length());

            // 验证相同内容的文件有相同的MD5
            File largeFile2 = createTestFile("large-test-file2.dat", largeContent.toString());
            String md5_2 = FileUtils.calculateMD5(largeFile2);
            assertEquals(md5, md5_2);

            // 清理第二个文件
            if (largeFile2.exists()) {
                largeFile2.delete();
            }
        } finally {
            // 清理大文件
            if (largeFile.exists()) {
                largeFile.delete();
            }
        }
    }
    
    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, String content) throws IOException {
        File file = tempDir.resolve(fileName).toFile();
        Files.write(file.toPath(), content.getBytes("UTF-8"));
        return file;
    }
    
    /**
     * 测试传输监听器
     */
    private static class TestTransferListener implements TransferListener {
        private boolean startCalled = false;
        private boolean progressCalled = false;
        private boolean completedCalled = false;
        private boolean errorCalled = false;
        private int progressCallCount = 0;
        private TransferProgress finalProgress;
        
        public void onStart(TransferProgress progress) {
            startCalled = true;
        }

        public void onProgress(TransferProgress progress) {
            progressCalled = true;
            progressCallCount++;
            finalProgress = progress;
        }

        public void onCompleted(TransferProgress progress) {
            completedCalled = true;
            finalProgress = progress;
        }

        public void onError(TransferProgress progress, Throwable error) {
            errorCalled = true;
        }
        
        // Getters
        public boolean isStartCalled() { return startCalled; }
        public boolean isProgressCalled() { return progressCalled; }
        public boolean isCompletedCalled() { return completedCalled; }
        public boolean isErrorCalled() { return errorCalled; }
        public int getProgressCallCount() { return progressCallCount; }
        public TransferProgress getFinalProgress() { return finalProgress; }
    }
}

#!/bin/bash

# 文件传输SDK完整CI/CD构建脚本
# 功能：结合编译和测试的完整流程，支持持续集成和持续部署
# 作者：Augment Agent
# 版本：1.0.0

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="文件传输SDK完整CI/CD构建脚本"

# Java 8 JDK路径配置
readonly JAVA8_HOME="$HOME/.jdks/corretto-1.8.0_452"

# 项目信息
readonly PROJECT_NAME="文件传输SDK"
readonly PROJECT_VERSION="1.0.0"

# 构建配置
readonly BUILD_TIMEOUT=900  # 构建超时时间（秒）
readonly TEST_TIMEOUT=1200  # 测试超时时间（秒）

# 日志文件路径
readonly LOG_DIR="./logs"
readonly CI_LOG="$LOG_DIR/ci-build-$(date +%Y%m%d_%H%M%S).log"
readonly ARTIFACT_DIR="$LOG_DIR/artifacts"

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_PURPLE='\033[0;35m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
    echo "[INFO] ${timestamp} - ${message}" >> "$CI_LOG"
}

# 成功日志
log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
    echo "[SUCCESS] ${timestamp} - ${message}" >> "$CI_LOG"
}

# 警告日志
log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
    echo "[WARNING] ${timestamp} - ${message}" >> "$CI_LOG"
}

# 错误日志
log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
    echo "[ERROR] ${timestamp} - ${message}" >> "$CI_LOG"
}

# 阶段日志
log_stage() {
    local stage_number="$1"
    local stage_name="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo ""
    echo "=========================================="
    echo -e "${COLOR_PURPLE}[STAGE ${stage_number}]${COLOR_NC} ${stage_name}"
    echo "时间：${timestamp}"
    echo "=========================================="
    echo "[STAGE ${stage_number}] ${timestamp} - ${stage_name}" >> "$CI_LOG"
}

# ==================== 工具函数 ====================

# 初始化CI环境
init_ci_environment() {
    log_info "初始化CI/CD环境..."
    
    # 创建必要的目录
    mkdir -p "$LOG_DIR"
    mkdir -p "$ARTIFACT_DIR"
    
    # 创建CI日志文件
    touch "$CI_LOG"
    
    # 记录环境信息
    {
        echo "=========================================="
        echo "        CI/CD环境信息"
        echo "=========================================="
        echo "构建时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "操作系统：$(uname -a)"
        echo "用户：$(whoami)"
        echo "工作目录：$(pwd)"
        echo "脚本版本：$SCRIPT_VERSION"
        echo "=========================================="
    } >> "$CI_LOG"
    
    log_info "CI日志文件：$CI_LOG"
    log_info "构建产物目录：$ARTIFACT_DIR"
}

# 显示脚本头部信息
show_header() {
    echo "=========================================="
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    项目：$PROJECT_NAME v$PROJECT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "=========================================="
}

# 执行构建脚本
execute_build() {
    log_stage "1" "项目编译构建"
    
    local build_start_time=$(date +%s)
    
    # 检查构建脚本是否存在
    if [ ! -f "./build.sh" ]; then
        log_error "构建脚本不存在：./build.sh"
        return 1
    fi
    
    # 确保构建脚本可执行
    chmod +x ./build.sh
    
    # 执行构建脚本
    log_info "执行构建脚本..."
    if timeout "$BUILD_TIMEOUT" ./build.sh --no-report >> "$CI_LOG" 2>&1; then
        local build_end_time=$(date +%s)
        local build_duration=$((build_end_time - build_start_time))
        
        log_success "项目构建成功，耗时：${build_duration}秒"
        return 0
    else
        local build_end_time=$(date +%s)
        local build_duration=$((build_end_time - build_start_time))
        
        log_error "项目构建失败，耗时：${build_duration}秒"
        return 1
    fi
}

# 执行测试脚本
execute_tests() {
    log_stage "2" "自动化测试"
    
    local test_start_time=$(date +%s)
    
    # 检查测试脚本是否存在
    if [ ! -f "./test.sh" ]; then
        log_error "测试脚本不存在：./test.sh"
        return 1
    fi
    
    # 确保测试脚本可执行
    chmod +x ./test.sh
    
    # 根据CI模式选择测试参数
    local test_args=""
    if [ "$CI_MODE" = "fast" ]; then
        test_args="--unit-only --skip-coverage"
        log_info "快速CI模式：仅运行单元测试"
    elif [ "$CI_MODE" = "full" ]; then
        test_args="--performance"
        log_info "完整CI模式：运行所有测试包括性能测试"
    else
        test_args=""
        log_info "标准CI模式：运行单元测试和集成测试"
    fi
    
    # 执行测试脚本
    log_info "执行测试脚本..."
    if timeout "$TEST_TIMEOUT" ./test.sh $test_args --no-report >> "$CI_LOG" 2>&1; then
        local test_end_time=$(date +%s)
        local test_duration=$((test_end_time - test_start_time))
        
        log_success "自动化测试通过，耗时：${test_duration}秒"
        return 0
    else
        local test_end_time=$(date +%s)
        local test_duration=$((test_end_time - test_start_time))
        
        log_error "自动化测试失败，耗时：${test_duration}秒"
        return 1
    fi
}

# 收集构建产物
collect_artifacts() {
    log_stage "3" "收集构建产物"
    
    log_info "收集JAR文件和文档..."
    
    # 收集JAR文件
    local jar_count=0
    find . -name "*.jar" -path "*/target/*" | while read jar_file; do
        if [ -f "$jar_file" ]; then
            local jar_name=$(basename "$jar_file")
            local module_name=$(echo "$jar_file" | cut -d'/' -f2)
            local target_name="${module_name}-${jar_name}"
            
            cp "$jar_file" "$ARTIFACT_DIR/$target_name"
            log_info "收集JAR文件：$target_name"
            jar_count=$((jar_count + 1))
        fi
    done
    
    # 收集测试报告
    if [ -d "./logs" ]; then
        cp -r ./logs/* "$ARTIFACT_DIR/" 2>/dev/null || true
        log_info "收集测试报告和日志文件"
    fi
    
    # 收集项目文档
    local docs=("README.md" "TESTING.md" "integration-guide.md")
    for doc in "${docs[@]}"; do
        if [ -f "$doc" ]; then
            cp "$doc" "$ARTIFACT_DIR/"
            log_info "收集文档：$doc"
        fi
    done
    
    # 生成构建产物清单
    {
        echo "=========================================="
        echo "        构建产物清单"
        echo "=========================================="
        echo "构建时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "项目版本：$PROJECT_VERSION"
        echo ""
        echo "JAR文件："
        find "$ARTIFACT_DIR" -name "*.jar" | while read jar_file; do
            local jar_size=$(du -h "$jar_file" | cut -f1)
            echo "  $(basename "$jar_file"): $jar_size"
        done
        echo ""
        echo "文档文件："
        find "$ARTIFACT_DIR" -name "*.md" | while read doc_file; do
            echo "  $(basename "$doc_file")"
        done
        echo ""
        echo "测试报告："
        find "$ARTIFACT_DIR" -name "*report*.txt" | while read report_file; do
            echo "  $(basename "$report_file")"
        done
        echo "=========================================="
    } > "$ARTIFACT_DIR/build-manifest.txt"
    
    log_success "构建产物收集完成：$ARTIFACT_DIR"
    return 0
}

# 质量检查
quality_check() {
    log_stage "4" "代码质量检查"
    
    log_info "执行代码质量检查..."
    
    # 检查编译警告
    local warning_count=0
    if [ -f "$CI_LOG" ]; then
        warning_count=$(grep -c "WARNING" "$CI_LOG" || true)
        log_info "编译警告数量：$warning_count"
    fi
    
    # 检查测试覆盖率（如果有）
    local coverage_files=$(find . -name "index.html" -path "*/jacoco/*" | wc -l)
    if [ "$coverage_files" -gt 0 ]; then
        log_info "找到 $coverage_files 个覆盖率报告"
    else
        log_warning "未找到测试覆盖率报告"
    fi
    
    # 检查代码规范（简单检查）
    local java_files=$(find . -name "*.java" -not -path "*/target/*" | wc -l)
    log_info "Java源文件数量：$java_files"
    
    # 检查是否有TODO或FIXME注释
    local todo_count=$(find . -name "*.java" -not -path "*/target/*" -exec grep -l "TODO\|FIXME" {} \; | wc -l)
    if [ "$todo_count" -gt 0 ]; then
        log_warning "发现 $todo_count 个文件包含TODO或FIXME注释"
    fi
    
    log_success "代码质量检查完成"
    return 0
}

# 生成CI报告
generate_ci_report() {
    log_stage "5" "生成CI/CD报告"
    
    local ci_report_file="$ARTIFACT_DIR/ci-build-report-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=========================================="
        echo "        CI/CD构建报告"
        echo "=========================================="
        echo "项目名称：$PROJECT_NAME"
        echo "项目版本：$PROJECT_VERSION"
        echo "构建时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "脚本版本：$SCRIPT_VERSION"
        echo ""
        
        echo "环境信息："
        echo "  操作系统：$(uname -s) $(uname -r)"
        echo "  Java版本：$(java -version 2>&1 | head -n 1)"
        echo "  Maven版本：$(mvn -version | head -n 1)"
        echo ""
        
        echo "构建结果："
        if [ -f "$ARTIFACT_DIR/build-manifest.txt" ]; then
            cat "$ARTIFACT_DIR/build-manifest.txt"
        else
            echo "  构建产物清单未生成"
        fi
        echo ""
        
        echo "测试结果："
        local test_reports=$(find "$ARTIFACT_DIR" -name "*test-report*.txt" | head -n 1)
        if [ -n "$test_reports" ] && [ -f "$test_reports" ]; then
            tail -n 20 "$test_reports"
        else
            echo "  测试报告未找到"
        fi
        echo ""
        
        echo "构建日志：$CI_LOG"
        echo "构建产物：$ARTIFACT_DIR"
        echo "=========================================="
        
    } > "$ci_report_file"
    
    log_info "CI/CD报告已生成：$ci_report_file"
    
    # 显示报告摘要
    echo ""
    echo "=========================================="
    echo "           CI/CD构建摘要"
    echo "=========================================="
    cat "$ci_report_file"
    
    return 0
}

# 清理函数（脚本退出时调用）
cleanup_on_exit() {
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        log_error "CI/CD构建过程中发生错误，退出码：$exit_code"
        log_info "详细错误信息请查看日志文件：$CI_LOG"
    fi
    
    # 确保停止所有后台进程
    jobs -p | xargs -r kill 2>/dev/null || true
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --mode=MODE       CI模式：fast（快速）、full（完整）、standard（标准，默认）"
    echo "  --skip-tests      跳过测试阶段"
    echo "  --skip-quality    跳过质量检查"
    echo "  --verbose         显示详细输出"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "CI模式说明:"
    echo "  fast     - 仅编译和单元测试，适用于快速验证"
    echo "  standard - 编译、单元测试和集成测试（默认）"
    echo "  full     - 完整流程包括性能测试，适用于发布前验证"
    echo ""
    echo "示例:"
    echo "  $0                    # 标准CI流程"
    echo "  $0 --mode=fast        # 快速CI流程"
    echo "  $0 --mode=full        # 完整CI流程"
    echo "  $0 --skip-tests       # 跳过测试"
    echo ""
}

# 主函数
main() {
    # 设置退出时清理
    trap cleanup_on_exit EXIT
    
    # 解析命令行参数
    export CI_MODE="standard"
    local skip_tests=false
    local skip_quality=false
    local verbose=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mode=*)
                CI_MODE="${1#*=}"
                shift
                ;;
            --skip-tests)
                skip_tests=true
                shift
                ;;
            --skip-quality)
                skip_quality=true
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证CI模式
    if [[ ! "$CI_MODE" =~ ^(fast|standard|full)$ ]]; then
        log_error "无效的CI模式：$CI_MODE"
        show_help
        exit 1
    fi
    
    # 显示脚本头部信息
    show_header
    
    # 初始化CI环境
    init_ci_environment
    
    # 如果启用详细模式，设置bash调试
    if [ "$verbose" = true ]; then
        set -x
        log_info "启用详细输出模式"
    fi
    
    log_info "CI模式：$CI_MODE"
    
    # 执行CI/CD流程
    local ci_failed=false
    
    # 阶段1：构建
    if ! execute_build; then
        ci_failed=true
    # 阶段2：测试
    elif [ "$skip_tests" = false ] && ! execute_tests; then
        ci_failed=true
    # 阶段3：收集产物
    elif ! collect_artifacts; then
        ci_failed=true
    # 阶段4：质量检查
    elif [ "$skip_quality" = false ] && ! quality_check; then
        log_warning "质量检查未通过，但不影响构建结果"
    fi
    
    # 阶段5：生成报告
    generate_ci_report
    
    # 返回结果
    if [ "$ci_failed" = true ]; then
        log_error "CI/CD构建失败"
        exit 1
    else
        log_success "CI/CD构建成功完成"
        exit 0
    fi
}

# 执行主函数
main "$@"

# 文件传输SDK自动化构建和测试指南

本文档详细说明如何使用自动化构建和测试脚本来编译、测试和部署文件传输SDK项目。

## 概述

我们为文件传输SDK项目创建了一套完整的自动化构建和测试解决方案，包括：

- **Java 8环境配置脚本** (`setup-java8.sh`) - 自动配置Java 8开发环境
- **自动编译脚本** (`build.sh`) - 编译整个项目并生成构建产物
- **自动测试脚本** (`test.sh`) - 运行完整的测试套件
- **CI/CD集成脚本** (`ci-build.sh`) - 完整的持续集成流程

## 脚本特性

### 🔧 技术特性
- **Java 8兼容性** - 严格遵循Java 8语法和API
- **Maven集成** - 完整的Maven生命周期管理
- **并行构建** - 使用Maven并行编译提高构建速度
- **超时控制** - 防止构建和测试过程无限等待
- **详细日志** - 完整的构建和测试日志记录

### 📊 测试覆盖
- **单元测试** - 各个组件的独立功能测试
- **集成测试** - 组件间协作测试
- **端到端测试** - 完整的客户端-服务端交互测试
- **性能测试** - 系统性能指标验证
- **覆盖率报告** - JaCoCo测试覆盖率分析

### 🚀 自动化功能
- **环境检查** - 自动验证Java和Maven环境
- **依赖管理** - 自动处理模块间依赖关系
- **错误处理** - 完善的错误检测和报告机制
- **产物收集** - 自动收集JAR文件和文档
- **报告生成** - 详细的构建和测试报告

## 快速开始

### 1. 环境准备

首先配置Java 8环境：

```bash
# 配置Java 8环境（仅当前会话）
./setup-java8.sh

# 配置Java 8环境并持久化到shell配置文件
./setup-java8.sh --persistent

# 验证Java 8配置
./setup-java8.sh --verify-only
```

### 2. 快速构建

```bash
# 完整构建流程（推荐）
./ci-build.sh

# 仅编译项目
./build.sh

# 仅运行测试
./test.sh
```

### 3. 查看结果

构建完成后，查看生成的文件：

```bash
# 查看构建日志
ls -la logs/

# 查看构建产物
ls -la logs/artifacts/

# 查看测试报告
cat logs/test-report-*.txt
```

## 详细使用说明

### Java 8环境配置脚本 (`setup-java8.sh`)

#### 功能说明
- 自动查找系统中的Java 8安装
- 配置JAVA_HOME和PATH环境变量
- 配置Maven使用Java 8编译
- 支持持久化配置到shell配置文件

#### 使用方法

```bash
# 基本用法 - 配置当前会话
./setup-java8.sh

# 持久化配置 - 写入shell配置文件
./setup-java8.sh --persistent

# 仅验证当前配置
./setup-java8.sh --verify-only

# 显示当前配置信息
./setup-java8.sh --show-config

# 显示帮助信息
./setup-java8.sh --help
```

#### 配置路径
脚本会按以下优先级查找Java 8：
1. `~/.jdks/corretto-1.8.0_452/` (指定路径)
2. `/usr/lib/jvm/java-8-*` (Linux系统路径)
3. `/Library/Java/JavaVirtualMachines/jdk1.8.0_*.jdk/Contents/Home` (macOS路径)
4. 系统PATH中的java命令

### 自动编译脚本 (`build.sh`)

#### 功能说明
- 自动检查Java 8和Maven环境
- 清理之前的构建产物
- 并行编译所有项目模块
- 安装到本地Maven仓库
- 验证编译结果
- 生成详细的构建报告

#### 使用方法

```bash
# 完整构建流程
./build.sh

# 仅编译，不安装到本地仓库
./build.sh --compile-only

# 跳过编译结果验证
./build.sh --skip-verify

# 不生成构建报告
./build.sh --no-report

# 显示详细输出
./build.sh --verbose

# 显示帮助信息
./build.sh --help
```

#### 构建产物
- **JAR文件** - 各模块的编译产物
- **源码JAR** - 包含源代码的JAR文件
- **Javadoc JAR** - API文档JAR文件
- **构建日志** - 详细的编译日志
- **构建报告** - 构建结果汇总

### 自动测试脚本 (`test.sh`)

#### 功能说明
- 运行单元测试、集成测试、性能测试
- 自动启动和停止测试服务器
- 生成测试覆盖率报告
- 收集和分析测试结果
- 生成详细的测试报告

#### 使用方法

```bash
# 运行完整测试套件
./test.sh

# 仅运行单元测试
./test.sh --unit-only

# 仅运行集成测试
./test.sh --integration-only

# 包含性能测试
./test.sh --performance

# 跳过覆盖率报告生成
./test.sh --skip-coverage

# 测试前不清理环境
./test.sh --no-cleanup

# 不生成测试报告
./test.sh --no-report

# 显示详细输出
./test.sh --verbose

# 显示帮助信息
./test.sh --help
```

#### 测试类型

**单元测试**
- 服务端SDK单元测试
- 客户端SDK单元测试
- 工具类和配置类测试

**集成测试**
- 端到端文件传输测试
- 客户端-服务端交互测试
- API接口测试

**性能测试**
- 文件传输性能测试
- 并发处理性能测试
- 内存使用测试

### CI/CD集成脚本 (`ci-build.sh`)

#### 功能说明
- 完整的持续集成流程
- 支持多种CI模式
- 自动收集构建产物
- 代码质量检查
- 生成CI/CD报告

#### 使用方法

```bash
# 标准CI流程
./ci-build.sh

# 快速CI模式（仅单元测试）
./ci-build.sh --mode=fast

# 完整CI模式（包含性能测试）
./ci-build.sh --mode=full

# 跳过测试阶段
./ci-build.sh --skip-tests

# 跳过质量检查
./ci-build.sh --skip-quality

# 显示详细输出
./ci-build.sh --verbose

# 显示帮助信息
./ci-build.sh --help
```

#### CI模式说明

**fast模式**
- 编译项目
- 运行单元测试
- 跳过覆盖率报告
- 适用于快速验证

**standard模式（默认）**
- 编译项目
- 运行单元测试和集成测试
- 生成覆盖率报告
- 适用于日常开发

**full模式**
- 编译项目
- 运行所有测试包括性能测试
- 生成完整报告
- 适用于发布前验证

## 高级配置

### 环境变量配置

```bash
# Java 8 JDK路径（可自定义）
export JAVA8_HOME="$HOME/.jdks/corretto-1.8.0_452"

# Maven选项配置
export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g"

# 测试服务器端口（可自定义）
export TEST_SERVER_PORT=49012
```

### 超时时间配置

在脚本中可以修改以下常量：

```bash
# 编译超时时间（秒）
readonly COMPILE_TIMEOUT=600

# 测试超时时间（秒）
readonly TEST_TIMEOUT=1200

# 服务器启动超时时间（秒）
readonly SERVER_STARTUP_TIMEOUT=30
```

### 日志级别配置

```bash
# 启用详细日志
./build.sh --verbose
./test.sh --verbose
./ci-build.sh --verbose

# 查看实时日志
tail -f logs/build-*.log
tail -f logs/test-*.log
tail -f logs/ci-build-*.log
```

## 故障排除

### 常见问题

#### 1. Java环境问题

**问题**: Java版本不兼容
```bash
# 解决方案
./setup-java8.sh --persistent
source ~/.bashrc
```

**问题**: JAVA_HOME未设置
```bash
# 解决方案
export JAVA_HOME="$HOME/.jdks/corretto-1.8.0_452"
export PATH="$JAVA_HOME/bin:$PATH"
```

#### 2. 编译问题

**问题**: 编译超时
```bash
# 解决方案：增加超时时间或使用更快的网络
./build.sh --verbose  # 查看详细错误信息
```

**问题**: 依赖下载失败
```bash
# 解决方案：清理Maven缓存
rm -rf ~/.m2/repository
./build.sh
```

#### 3. 测试问题

**问题**: 端口被占用
```bash
# 解决方案：查找并停止占用端口的进程
lsof -ti:49012 | xargs kill -9
./test.sh
```

**问题**: 测试服务器启动失败
```bash
# 解决方案：检查日志并手动启动
cat logs/test-*.log
cd file-transfer-demo
mvn spring-boot:run
```

#### 4. 权限问题

**问题**: 脚本无执行权限
```bash
# 解决方案
chmod +x *.sh
```

**问题**: 日志目录无写权限
```bash
# 解决方案
mkdir -p logs
chmod 755 logs
```

### 调试技巧

#### 1. 启用详细输出
```bash
./build.sh --verbose
./test.sh --verbose
./ci-build.sh --verbose
```

#### 2. 查看实时日志
```bash
# 在另一个终端窗口中
tail -f logs/build-*.log
tail -f logs/test-*.log
```

#### 3. 分步执行
```bash
# 分别执行各个阶段
./setup-java8.sh
./build.sh --compile-only
./test.sh --unit-only
./test.sh --integration-only
```

#### 4. 手动验证
```bash
# 验证Java环境
java -version
javac -version
mvn -version

# 验证项目结构
ls -la
cat pom.xml

# 验证编译结果
ls -la */target/
```

## 最佳实践

### 1. 开发流程

```bash
# 每日开发流程
./setup-java8.sh              # 配置环境
./ci-build.sh --mode=fast     # 快速验证
# 进行代码开发
./test.sh --unit-only         # 运行单元测试
./ci-build.sh                 # 完整验证
```

### 2. 发布前验证

```bash
# 发布前完整验证
./setup-java8.sh --verify-only
./ci-build.sh --mode=full
# 检查所有报告
cat logs/ci-build-report-*.txt
```

### 3. 持续集成

```bash
# CI/CD流水线脚本
#!/bin/bash
set -e

# 环境准备
./setup-java8.sh

# 根据分支选择CI模式
if [ "$BRANCH" = "main" ]; then
    ./ci-build.sh --mode=full
else
    ./ci-build.sh --mode=standard
fi

# 上传构建产物
cp -r logs/artifacts/* $ARTIFACT_UPLOAD_PATH/
```

### 4. 性能监控

```bash
# 定期性能测试
./test.sh --performance
# 分析性能报告
grep "性能测试" logs/test-*.log
```

## 总结

通过使用这套自动化构建和测试脚本，您可以：

1. **快速配置** - 一键配置Java 8开发环境
2. **自动构建** - 自动编译整个项目并处理依赖
3. **全面测试** - 运行完整的测试套件确保代码质量
4. **持续集成** - 支持CI/CD流程的完整自动化
5. **详细报告** - 获得详细的构建和测试报告

所有脚本都包含详细的中文注释、完善的错误处理和日志记录，确保构建过程的可靠性和可维护性。

如有任何问题，请查看相应的日志文件或使用 `--help` 选项获取详细帮助信息。

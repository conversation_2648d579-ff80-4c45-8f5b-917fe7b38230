package com.sdesrd.filetransfer.demo;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.TestPropertySource;

import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.FileUtils;
import com.sdesrd.filetransfer.server.util.FileTransferTestUtils;

/**
 * 端到端文件传输测试
 * 测试完整的客户端-服务端文件传输流程
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "file.transfer.server.enabled=true",
    "file.transfer.server.users.test-user.secret-key=test-secret-key-2024",
    "file.transfer.server.users.test-user.storage-path=./test-data/storage",
    "file.transfer.server.users.test-user.upload-rate-limit=52428800",
    "file.transfer.server.users.test-user.download-rate-limit=52428800",
    "file.transfer.server.users.test-user.max-file-size=104857600",
    "file.transfer.server.users.test-user.rate-limit-enabled=false"
})
@DisplayName("端到端文件传输测试")
class EndToEndTransferTest {
    
    @LocalServerPort
    private int serverPort;
    
    @TempDir
    Path tempDir;
    
    private FileTransferClient client;
    private TestTransferListener listener;
    
    @BeforeEach
    void setUp() {
        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("localhost")
                .serverPort(serverPort)
                .auth("test-user", "test-secret-key-2024")
                .chunkSize(512 * 1024) // 512KB 分块
                .maxConcurrentTransfers(3)
                .retry(3, 1000)
                .build();
        
        client = new FileTransferClient(config);
        listener = new TestTransferListener();
    }
    
    @AfterEach
    void tearDown() {
        if (client != null) {
            client.close();
        }
    }
    
    @Test
    @DisplayName("小文件完整传输流程测试")
    void testSmallFileCompleteTransfer() throws Exception {
        // 创建小测试文件（1KB）
        File originalFile = createTestFile("small-test.txt", "小文件测试内容\n".repeat(50));
        
        // 上传文件
        UploadResult uploadResult = client.uploadFileSync(originalFile.getAbsolutePath(), null, listener);
        
        // 验证上传结果
        assertTrue(uploadResult.isSuccess());
        assertNotNull(uploadResult.getFileId());
        assertNotNull(uploadResult.getTransferId());
        
        // 验证监听器被调用
        assertTrue(listener.isStartCalled());
        assertTrue(listener.isCompletedCalled());
        
        // 下载文件
        File downloadFile = tempDir.resolve("downloaded-small-test.txt").toFile();
        TestTransferListener downloadListener = new TestTransferListener();
        
        DownloadResult downloadResult = client.downloadFileSync(uploadResult.getFileId(), 
                downloadFile.getAbsolutePath(), downloadListener);
        
        // 验证下载结果
        assertTrue(downloadResult.isSuccess());
        assertEquals(downloadFile.getAbsolutePath(), downloadResult.getLocalPath());
        
        // 验证文件完整性
        assertTrue(downloadFile.exists());
        assertEquals(originalFile.length(), downloadFile.length());
        
        String originalMd5 = FileUtils.calculateMD5(originalFile);
        String downloadedMd5 = FileUtils.calculateMD5(downloadFile);
        assertEquals(originalMd5, downloadedMd5);
        
        // 验证下载监听器被调用
        assertTrue(downloadListener.isCompletedCalled());
    }
    
    @Test
    @DisplayName("中等文件分块传输测试")
    void testMediumFileChunkedTransfer() throws Exception {
        // 创建中等测试文件（2MB）
        File originalFile = createTestFile("medium-test.dat", "X".repeat(1024).repeat(2048));
        
        // 异步上传文件
        CompletableFuture<UploadResult> uploadFuture = client.uploadFile(
                originalFile.getAbsolutePath(), "medium-test-uploaded.dat", listener);
        
        // 等待上传完成
        UploadResult uploadResult = uploadFuture.get(60, TimeUnit.SECONDS);
        
        // 验证上传结果
        assertTrue(uploadResult.isSuccess());
        assertNotNull(uploadResult.getFileId());
        
        // 验证进度监听器被多次调用（因为是分块传输）
        assertTrue(listener.getProgressCallCount() > 1);
        
        // 验证最终进度
        TransferProgress finalProgress = listener.getFinalProgress();
        assertNotNull(finalProgress);
        assertEquals(100.0, finalProgress.getProgress(), 0.01);
        assertEquals(originalFile.length(), finalProgress.getTotalSize());
        
        // 异步下载文件
        File downloadFile = tempDir.resolve("downloaded-medium-test.dat").toFile();
        TestTransferListener downloadListener = new TestTransferListener();
        
        CompletableFuture<DownloadResult> downloadFuture = client.downloadFile(
                uploadResult.getFileId(), downloadFile.getAbsolutePath(), downloadListener);
        
        DownloadResult downloadResult = downloadFuture.get(60, TimeUnit.SECONDS);
        
        // 验证下载结果
        assertTrue(downloadResult.isSuccess());
        
        // 验证文件完整性
        assertTrue(FileTransferTestUtils.verifyFilesEqual(
                originalFile.getAbsolutePath(), downloadFile.getAbsolutePath()));
    }
    
    @Test
    @DisplayName("大文件传输性能测试")
    void testLargeFileTransferPerformance() throws Exception {
        // 创建大测试文件（10MB）
        String largeFilePath = tempDir.resolve("large-test.dat").toString();
        File originalFile = FileTransferTestUtils.createTestFile(largeFilePath, 10 * 1024 * 1024);
        
        // 记录上传开始时间
        long uploadStartTime = System.currentTimeMillis();
        
        // 上传文件
        UploadResult uploadResult = client.uploadFileSync(originalFile.getAbsolutePath(), null, listener);
        
        long uploadEndTime = System.currentTimeMillis();
        long uploadDuration = uploadEndTime - uploadStartTime;
        
        // 验证上传结果
        assertTrue(uploadResult.isSuccess());
        
        // 计算上传性能
        double uploadSizeMB = originalFile.length() / (1024.0 * 1024.0);
        double uploadThroughputMBps = (uploadSizeMB * 1000.0) / uploadDuration;
        
        System.out.printf("大文件上传性能 - 大小: %.2fMB, 耗时: %d毫秒, 吞吐量: %.2f MB/s%n", 
                uploadSizeMB, uploadDuration, uploadThroughputMBps);
        
        // 性能验证：10MB文件上传应该在合理时间内完成
        assertTrue(uploadDuration < 30000, "上传耗时过长: " + uploadDuration + "ms");
        assertTrue(uploadThroughputMBps > 0.5, "上传吞吐量过低: " + uploadThroughputMBps + " MB/s");
        
        // 下载文件
        File downloadFile = tempDir.resolve("downloaded-large-test.dat").toFile();
        TestTransferListener downloadListener = new TestTransferListener();
        
        long downloadStartTime = System.currentTimeMillis();
        
        DownloadResult downloadResult = client.downloadFileSync(uploadResult.getFileId(), 
                downloadFile.getAbsolutePath(), downloadListener);
        
        long downloadEndTime = System.currentTimeMillis();
        long downloadDuration = downloadEndTime - downloadStartTime;
        
        // 验证下载结果
        assertTrue(downloadResult.isSuccess());
        
        // 计算下载性能
        double downloadThroughputMBps = (uploadSizeMB * 1000.0) / downloadDuration;
        
        System.out.printf("大文件下载性能 - 大小: %.2fMB, 耗时: %d毫秒, 吞吐量: %.2f MB/s%n", 
                uploadSizeMB, downloadDuration, downloadThroughputMBps);
        
        // 性能验证
        assertTrue(downloadDuration < 30000, "下载耗时过长: " + downloadDuration + "ms");
        assertTrue(downloadThroughputMBps > 0.5, "下载吞吐量过低: " + downloadThroughputMBps + " MB/s");
        
        // 验证文件完整性
        assertTrue(FileTransferTestUtils.verifyFilesEqual(
                originalFile.getAbsolutePath(), downloadFile.getAbsolutePath()));
    }
    
    @Test
    @DisplayName("秒传功能测试")
    void testFastUploadFeature() throws Exception {
        // 创建测试文件
        File originalFile = createTestFile("fast-upload-test.txt", "秒传测试内容\n".repeat(1000));
        
        // 第一次上传
        UploadResult firstUpload = client.uploadFileSync(originalFile.getAbsolutePath(), 
                "first-upload.txt", listener);
        assertTrue(firstUpload.isSuccess());
        
        // 第二次上传相同内容的文件（应该触发秒传）
        TestTransferListener secondListener = new TestTransferListener();
        long secondUploadStart = System.currentTimeMillis();
        
        UploadResult secondUpload = client.uploadFileSync(originalFile.getAbsolutePath(), 
                "second-upload.txt", secondListener);
        
        long secondUploadDuration = System.currentTimeMillis() - secondUploadStart;
        
        // 验证第二次上传结果
        assertTrue(secondUpload.isSuccess());
        assertNotNull(secondUpload.getFileId());
        
        // 秒传应该比正常上传快很多
        System.out.printf("秒传耗时: %d毫秒%n", secondUploadDuration);
        
        // 验证两次上传的文件ID相同（因为内容相同）
        assertEquals(firstUpload.getFileId(), secondUpload.getFileId());
    }
    
    @Test
    @DisplayName("传输进度查询测试")
    void testTransferProgressQuery() throws Exception {
        // 创建测试文件
        File originalFile = createTestFile("progress-test.txt", "进度查询测试\n".repeat(500));
        
        // 上传文件
        UploadResult uploadResult = client.uploadFileSync(originalFile.getAbsolutePath(), null, listener);
        assertTrue(uploadResult.isSuccess());
        
        // 查询传输进度
        TransferProgress progress = client.queryProgress(uploadResult.getTransferId());
        
        // 验证进度信息
        assertNotNull(progress);
        assertEquals(100.0, progress.getProgress(), 0.01);
        assertTrue(progress.isCompleted());
        assertEquals(originalFile.getName(), progress.getFileName());
        assertEquals(originalFile.length(), progress.getTotalSize());
        assertEquals(originalFile.length(), progress.getTransferredSize());
    }
    
    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, String content) throws IOException {
        File file = tempDir.resolve(fileName).toFile();
        Files.write(file.toPath(), content.getBytes("UTF-8"));
        return file;
    }
    
    /**
     * 测试传输监听器
     */
    private static class TestTransferListener implements TransferListener {
        private boolean startCalled = false;
        private boolean progressCalled = false;
        private boolean completedCalled = false;
        private boolean errorCalled = false;
        private int progressCallCount = 0;
        private TransferProgress finalProgress;
        
        @Override
        public void onStart(TransferProgress progress) {
            startCalled = true;
        }
        
        @Override
        public void onProgress(TransferProgress progress) {
            progressCalled = true;
            progressCallCount++;
            finalProgress = progress;
        }
        
        @Override
        public void onCompleted(TransferProgress progress) {
            completedCalled = true;
            finalProgress = progress;
        }
        
        @Override
        public void onError(TransferProgress progress, Throwable error) {
            errorCalled = true;
        }
        
        // Getters
        public boolean isStartCalled() { return startCalled; }
        public boolean isProgressCalled() { return progressCalled; }
        public boolean isCompletedCalled() { return completedCalled; }
        public boolean isErrorCalled() { return errorCalled; }
        public int getProgressCallCount() { return progressCallCount; }
        public TransferProgress getFinalProgress() { return finalProgress; }
    }
}
